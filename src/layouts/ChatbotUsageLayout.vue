<template>
  <q-layout view="hHh LpR lfr">
    <!-- Top Bar -->
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title class="text-weight-bold">
          <q-btn flat dense round icon="menu" aria-label="Menu" @click="toggleLeftDrawer">
            <q-tooltip>Toggle Left Drawer</q-tooltip>
          </q-btn>
          <q-btn
            flat
            no-caps
            label="Bytewise"
            :to="urlToHomepage(user.role_name)"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 5px"
          />
        </q-toolbar-title>
        <q-space />
        <div class="q-gutter-sm">
          <q-btn
            flat
            dense
            round
            icon="question_answer"
            @click="openConsultationMailbox"
            :color="hasUnreadInternalMessages ? 'red' : 'primary'"
          >
            <q-tooltip>Student Questions</q-tooltip>
            <q-badge v-if="unreadInternalMessagesCount > 0" color="red" floating>
              {{ unreadInternalMessagesCount }}
            </q-badge>
          </q-btn>
          <q-btn flat dense round icon="ios_share" @click="onClickShareChat">
            <q-tooltip>Share Chat</q-tooltip>
          </q-btn>
          <q-btn
            flat
            dense
            round
            icon="checklist"
            class="float-right"
            @click="isRightDrawerOpen = !isRightDrawerOpen"
          >
            <q-tooltip>Toggle Right Drawer</q-tooltip>
          </q-btn>
        </div>
      </q-toolbar>
    </q-header>
    <q-drawer v-if="!isFetching" v-model="isLeftDrawerOpen" show-if-above side="left" bordered>
      <q-list bordered separator>
        <div class="q-pa-md text-h6 row q-gutter-sm">
          <q-btn flat dense round icon="arrow_back" @click="goToReviewChatbotUsage()">
            <q-tooltip>Back to Review Chatbot Usage Page</q-tooltip>
          </q-btn>
          <div>
            {{ courseTitle }}
          </div>
        </div>
        <q-expansion-item
          v-for="chatbot in chatbots"
          :key="chatbot.chatbot_id"
          :label="chatbot.chatbot_name"
          :model-value="chatbot.chatbot_id === globalChatbotId"
          icon="chat"
        >
          <q-item
            clickable
            v-for="session in chatbot.sessions"
            :key="session.session_id"
            :active="session.session_id === selectedSessionId"
            @click="
              selectSession(chatbot.chatbot_id, studentId, session.session_id, session.session_name)
            "
          >
            <q-item-section> {{ session.session_name }} </q-item-section>
          </q-item>
        </q-expansion-item>
      </q-list>
    </q-drawer>

    <!-- Add Right Drawer -->
    <q-drawer v-if="!isFetching" v-model="isRightDrawerOpen" side="right" bordered show-if-above>
      <q-scroll-area class="fit">
        <q-list>
          <!-- YouTube Video Player Section -->
          <div v-if="showYoutubePlayer && youtubeVideos.length > 0">
            <q-item-label header class="text-h6 q-pa-md text-dark">
              <div class="row items-center justify-between no-wrap">
                <div class="col row items-center no-wrap">
                  <q-icon name="video_library" class="q-mr-sm" color="primary" />
                  <span class="text-no-wrap">YouTube Videos</span>
                  <span
                    v-if="youtubeVideos.length > 1"
                    class="text-caption bg-primary text-white q-px-sm q-py-xs rounded-borders q-ml-sm"
                  >
                    {{ currentVideoIndex + 1 }} of {{ youtubeVideos.length }}
                  </span>
                </div>
                <div class="col-auto">
                  <q-btn
                    flat
                    dense
                    round
                    :icon="isYoutubePlayerMinimized ? 'expand_more' : 'expand_less'"
                    @click="minimizeYoutubePlayer"
                    color="primary"
                    size="sm"
                  >
                    <q-tooltip>{{
                      isYoutubePlayerMinimized ? 'Expand Videos' : 'Minimize Videos'
                    }}</q-tooltip>
                  </q-btn>
                </div>
              </div>
            </q-item-label>

            <div v-if="!isYoutubePlayerMinimized">
              <!-- Video iframe container -->
              <div class="youtube-container q-pa-md">
                <iframe
                  :src="currentYoutubeEmbedUrl"
                  frameborder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowfullscreen
                  class="youtube-iframe"
                ></iframe>
              </div>

              <!-- Current Video Info (below video iframe) -->
              <q-card
                v-if="youtubeVideos.length > 0 && youtubeVideos[currentVideoIndex]"
                flat
                bordered
                class="q-mx-md bg-blue-1"
              >
                <q-card-section class="q-py-sm">
                  <div class="text-caption text-primary row items-center">
                    <q-icon name="play_circle_filled" size="sm" class="q-mr-sm" color="primary" />
                    <span class="text-weight-medium">
                      Video {{ currentVideoIndex + 1 }}:
                      {{ youtubeVideos[currentVideoIndex]?.title }}
                    </span>
                  </div>
                </q-card-section>
              </q-card>

              <!-- Video Navigation Controls (completely below video) -->
              <q-card
                v-if="youtubeVideos.length > 1"
                flat
                bordered
                class="q-mx-md q-mb-sm bg-grey-1"
              >
                <q-card-section class="q-py-sm">
                  <div class="row items-center no-wrap q-gutter-sm">
                    <div class="col-auto">
                      <q-btn
                        flat
                        dense
                        round
                        icon="chevron_left"
                        @click="previousVideo"
                        :disable="currentVideoIndex === 0"
                        color="primary"
                      >
                        <q-tooltip>Previous Video</q-tooltip>
                      </q-btn>
                      <q-btn
                        flat
                        dense
                        round
                        icon="chevron_right"
                        @click="nextVideo"
                        :disable="currentVideoIndex === youtubeVideos.length - 1"
                        color="primary"
                      >
                        <q-tooltip>Next Video</q-tooltip>
                      </q-btn>
                    </div>

                    <!-- Video Selection Dropdown -->
                    <div class="col">
                      <q-select
                        v-model="currentVideoIndex"
                        dense
                        outlined
                        :options="videoSelectOptions"
                        option-value="value"
                        option-label="label"
                        emit-value
                        map-options
                        bg-color="white"
                        color="primary"
                      >
                        <template v-slot:prepend>
                          <q-icon name="video_library" color="primary" />
                        </template>
                        <q-tooltip
                          v-if="youtubeVideos[currentVideoIndex]?.title"
                          class="bg-primary"
                        >
                          Full title: {{ youtubeVideos[currentVideoIndex]?.title }}
                        </q-tooltip>
                      </q-select>
                    </div>

                    <div class="col-auto">
                      <!-- Navigation hint for mobile users -->
                      <q-chip dense color="primary" text-color="white" class="text-caption">
                        {{ currentVideoIndex + 1 }} / {{ youtubeVideos.length }}
                      </q-chip>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </div>

          <!-- Add Session Summary Section -->
          <div v-show="chatSessionDescription.qualitative_report.session_summary">
            <q-item-label header class="text-h6 q-pa-md text-dark"> Session Summary </q-item-label>

            <q-item-section>
              <div
                v-if="chatSessionDescription.qualitative_report.session_summary"
                class="sessionsummary-container q-px-md"
              >
                <div
                  v-html="
                    markdown.render(chatSessionDescription.qualitative_report.session_summary)
                  "
                ></div>
              </div>
              <div v-else class="text-center text-grey">No session summary available.</div>
            </q-item-section>
          </div>

          <!-- Add Progress Checklist Section (only for ChecklistChatBot) -->
          <div
            v-if="
              currentChatbotDetail.type_name === 'ChecklistChatBot' &&
              chatSessionDescription.checklist_progress
            "
          >
            <q-item-label header class="text-h6 q-pa-md text-dark">
              Progress Checklist
            </q-item-label>

            <template v-if="Array.isArray(chatSessionDescription.checklist_progress)">
              <!-- Progress Summary -->
              <q-item>
                <q-item-section>
                  <q-linear-progress :value="checklistProgress" color="positive" />
                  <div class="text-caption text-center q-mt-sm">
                    {{ Math.round(checklistProgress * 100) }}% Complete
                  </div>
                </q-item-section>
              </q-item>

              <q-item v-for="item in chatSessionDescription.checklist_progress" :key="item.item_id">
                <q-item-section avatar>
                  <q-checkbox
                    v-model="item.completed"
                    disable
                    :color="item.completed ? 'positive' : 'grey'"
                  />
                </q-item-section>
                <q-item-section>
                  <q-item-label :class="{ 'text-positive': item.completed }">
                    {{ item.item_content }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </template>

            <q-item v-else>
              <q-item-section class="text-center text-grey">
                No checklist items available
              </q-item-section>
            </q-item>
          </div>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- Consultation Mailbox Dialog -->
    <q-dialog v-model="showConsultationMailbox" persistent>
      <q-card style="width: 700px; max-width: 80vw">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Student Questions</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div class="text-subtitle2 q-mb-md">
            Session:
            {{
              selectedSessionId === 'latest'
                ? chatbots[0]?.sessions[0]?.session_name || 'New Session'
                : chatbots[0]?.sessions.find((s) => s.session_id === selectedSessionId)
                    ?.session_name || 'New Session'
            }}
          </div>

          <!-- Messages Area -->
          <div
            class="consultation-messages-container q-pa-md"
            style="height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px"
            ref="messagesContainer"
          >
            <div v-if="internalMessages.length === 0" class="text-center text-grey-6 q-mt-md">
              <q-icon name="message" size="md" class="q-mb-sm" />
              <div>No student questions yet</div>
            </div>

            <div v-for="message in internalMessages" :key="message.message_id" class="q-mb-md">
              <q-chat-message
                :name="message.sender_role === 'Student' ? 'Student' : 'Teacher'"
                :sent="message.sender_role === user.role_name"
                :text-color="message.sender_role === user.role_name ? 'white' : 'black'"
                :bg-color="message.sender_role === user.role_name ? 'blue-7' : 'grey-4'"
                :stamp="formatMessageTime(message.created_at || '')"
              >
                <div v-html="markdown.render(message.message_content)"></div>
              </q-chat-message>
            </div>
          </div>

          <!-- Message Input -->
          <div class="q-mt-md">
            <q-input
              v-model="newInternalMessage"
              type="textarea"
              label="Reply to student..."
              :rows="3"
              outlined
              @keyup.ctrl.enter="sendInternalMessage"
            >
              <template v-slot:append>
                <q-btn
                  round
                  dense
                  flat
                  icon="send"
                  @click="sendInternalMessage"
                  :disable="!newInternalMessage.trim()"
                  :loading="sendingInternalMessage"
                />
              </template>
            </q-input>
            <div class="text-caption text-grey-6 q-mt-xs">Press Ctrl+Enter to send</div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <q-page-container v-if="!isFetching">
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed, provide, nextTick, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import MarkdownIt from 'markdown-it';
import MarkdownItKatex from 'markdown-it-katex';
const markdown = new MarkdownIt();
markdown.use(MarkdownItKatex);

// Consultation mailbox interfaces
interface InternalMessage {
  message_id: string;
  session_id: string;
  sender_user_id: string;
  receiver_user_id: string;
  message_content: string;
  message_type: string;
  is_read: boolean;
  is_sent_to_chatbot: boolean;
  // Additional fields for frontend display (derived from joins)
  sender_role?: 'Student' | 'Teacher';
  receiver_role?: 'Student' | 'Teacher';
  created_at?: string;
}

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

const $q = useQuasar();
const router = useRouter();
const route = useRoute();

const isLeftDrawerOpen = ref(true);
const isRightDrawerOpen = ref(false);

const toggleLeftDrawer = () => {
  isLeftDrawerOpen.value = !isLeftDrawerOpen.value;
};

const goToReviewChatbotUsage = async () => {
  await router.push({
    path: `/teacher/chatbot/${globalChatbotId.value}/usage`,
    query: {
      chatbotName: chatbots.value[0]?.chatbot_name,
      courseId: courseId.value,
      courseTitle: courseTitle.value,
      moduleId: moduleId.value,
      moduleTitle: moduleTitle.value,
    },
  });
};

const chatbots = ref([
  {
    chatbot_name: 'Chatbot 1',
    chatbot_id: '1',
    sessions: [
      { session_id: '1', session_name: 'Session 1 (Latest)', session_index: 1 },
      { session_id: '2', session_name: 'Session 2', session_index: 2 },
    ],
  },
]);

const isFetching = ref(true);
const sessionId = ref<string>('');
const selectedSessionId = ref<string>('');
const globalChatbotId = ref<string>('');
const studentId = ref<string>('');
const courseId = ref<string>('');
const courseTitle = ref<string>('');
const moduleId = ref<string>('');
const moduleTitle = ref<string>('');

interface User {
  full_name: string;
  email: string;
  role_name: string;
}

const user = ref<User>({
  full_name: '',
  email: '',
  role_name: '',
});

// Add interfaces for chat session description
interface ChecklistProgressItem {
  item_id: string;
  item_content: string;
  completed: boolean;
}

interface QuantitativeReport {
  turn_count: number;
  user_word_count: number;
  chatbot_word_count: number;
  conversation_time: number;
}

interface QualitativeReport {
  session_summary: string;
}

interface UserFeedbackItem {
  rating: number;
  text_feedback: string;
}

interface ChatSessionDescription {
  checklist_progress: ChecklistProgressItem[];
  quantitative_report: QuantitativeReport;
  qualitative_report: QualitativeReport;
  user_feedback: UserFeedbackItem;
}

// Add interface for chatbot details
interface ChatbotDetail {
  chatbot_id: string;
  id: number;
  created_at: string;
  chatbot_name: string;
  model_name: string;
  system_prompt: string;
  welcome_prompt: string;
  temperature: number;
  type_name: string;
  description: {
    checklist_items: string;
    session_summary_prompt: string;
  };
  knowledge_base_persist_directory: string;
  knowledge_base_file_paths: string[];
  knowledge_base_file_names: string[];
  knowledge_base_embedding_model: string;
  updated_at: string;
  deleted_at: string;
}

// Initialize chatSessionDescription with a default structure
const chatSessionDescription = ref<ChatSessionDescription>({
  checklist_progress: [],
  quantitative_report: {
    turn_count: 0,
    user_word_count: 0,
    chatbot_word_count: 0,
    conversation_time: 0,
  },
  qualitative_report: {
    session_summary: '',
  },
  user_feedback: {
    rating: 0,
    text_feedback: '',
  },
});

// Add ref for chatbot details
const currentChatbotDetail = ref<ChatbotDetail>({
  chatbot_id: '',
  id: 0,
  created_at: '',
  chatbot_name: '',
  model_name: '',
  system_prompt: '',
  welcome_prompt: '',
  temperature: 0,
  type_name: '',
  description: {
    checklist_items: '',
    session_summary_prompt: '',
  },
  knowledge_base_persist_directory: '',
  knowledge_base_file_paths: [],
  knowledge_base_file_names: [],
  knowledge_base_embedding_model: '',
  updated_at: '',
  deleted_at: '',
});

// YouTube multiple videos detection logic
const showYoutubePlayer = ref(false);
const isYoutubePlayerMinimized = ref(false);
const youtubeVideos = ref<Array<{ id: string; url: string; title: string; messageIndex: number }>>(
  [],
);
const currentVideoIndex = ref(0);

// Extract YouTube video ID from URL
const extractYoutubeId = (url: string): string | null => {
  const regexPatterns = [
    /(?:youtube\.com\/(?:[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?/\s]{11})/i,
    /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)([^"&?/\s]{11})/i,
  ];

  for (const pattern of regexPatterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
};

// Current YouTube embed URL
const currentYoutubeEmbedUrl = computed(() => {
  const currentVideo = youtubeVideos.value[currentVideoIndex.value];
  if (youtubeVideos.value.length > 0 && currentVideo) {
    return `https://www.youtube.com/embed/${currentVideo.id}`;
  }
  return '';
});

// Video selection options for dropdown
const videoSelectOptions = computed(() => {
  return youtubeVideos.value.map((video, index) => ({
    label: `${index + 1}`,
    value: index,
  }));
});

// Navigation functions
const previousVideo = () => {
  if (currentVideoIndex.value > 0) {
    currentVideoIndex.value--;
  }
};

const nextVideo = () => {
  if (currentVideoIndex.value < youtubeVideos.value.length - 1) {
    currentVideoIndex.value++;
  }
};

// Minimize/Maximize YouTube player
const minimizeYoutubePlayer = () => {
  isYoutubePlayerMinimized.value = !isYoutubePlayerMinimized.value;
};

// Add computed property for checklist progress
const checklistProgress = computed(() => {
  const progress = chatSessionDescription.value?.checklist_progress;

  // Check if progress is available
  if (!progress || !Array.isArray(progress) || progress.length === 0) {
    return 0;
  }

  const completedItems = progress.filter((item) => item?.completed).length;
  return completedItems / progress.length;
});

// Function to load chat session description
const loadChatSessionDescription = async () => {
  try {
    const response = await api.get('/chat-session-description', {
      params: {
        session_id: sessionId.value,
      },
    });

    // Simply use the data from the backend as is
    chatSessionDescription.value = response.data.description || {
      checklist_progress: [],
      quantitative_report: {
        turn_count: 0,
        user_word_count: 0,
        chatbot_word_count: 0,
        conversation_time: 0,
      },
      qualitative_report: {
        session_summary: '',
      },
      user_feedback: {
        rating: 0,
        text_feedback: '',
      },
    };
  } catch (error) {
    console.error('Error loading chat session description:', error);
  }
};

// Function to load chatbot details
const loadChatbotDetails = async () => {
  try {
    const response = await api.get('/chatbot', {
      params: {
        chatbot_id: globalChatbotId.value,
      },
    });
    currentChatbotDetail.value = response.data;
  } catch (error) {
    console.error('Error loading chatbot details:', error);
  }
};

// Add new refs for API conversation
const apiConversation = ref<{ role: string; content: string }[]>([]);

// Consultation mailbox reactive variables
const showConsultationMailbox = ref(false);
const internalMessages = ref<InternalMessage[]>([]);
const newInternalMessage = ref('');
const sendingInternalMessage = ref(false);
const unreadInternalMessagesCount = ref(0);
const hasUnreadInternalMessages = computed(() => unreadInternalMessagesCount.value > 0);
const messagesContainer = ref<HTMLElement | null>(null);

// Watch for YouTube links in conversation
watch(
  apiConversation,
  (newConversation) => {
    if (!newConversation || newConversation.length === 0) return;

    const foundVideos: Array<{ id: string; url: string; title: string; messageIndex: number }> = [];

    // Search for YouTube links in all messages (preserve chronological order)
    for (let i = 0; i < newConversation.length; i++) {
      const message = newConversation[i];
      if (!message || !message.content) continue;

      // Find URLs in the message
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const urls = message.content.match(urlRegex);

      if (urls) {
        for (const url of urls) {
          const videoId = extractYoutubeId(url);
          if (videoId) {
            // Check if video is already added (avoid duplicates)
            if (!foundVideos.some((v) => v.id === videoId)) {
              foundVideos.push({
                id: videoId,
                url: url,
                title: `Video from message ${i}`,
                messageIndex: i,
              });
            }
          }
        }
      }
    }

    // Check if there are new videos by comparing with existing ones
    const hasNewVideos =
      foundVideos.length > youtubeVideos.value.length ||
      foundVideos.some(
        (video) => !youtubeVideos.value.some((existing) => existing.id === video.id),
      );

    // Update videos array
    youtubeVideos.value = foundVideos;

    // Handle video player visibility
    if (foundVideos.length > 0) {
      showYoutubePlayer.value = true;
      isYoutubePlayerMinimized.value = false; // Show expanded when new videos are found
      isRightDrawerOpen.value = true; // When there are YouTube links, automatically open the right drawer

      // Always show the latest video (last one in the array)
      if (hasNewVideos || currentVideoIndex.value >= foundVideos.length) {
        currentVideoIndex.value = foundVideos.length - 1; // Always go to the latest video
      }
    } else {
      // No YouTube links found
      showYoutubePlayer.value = false;
      isYoutubePlayerMinimized.value = false; // Reset minimized state
      currentVideoIndex.value = 0;
    }
  },
  { deep: true },
);

// Helper function to get session share link
const getSessionShareLink = (sessionShareId: string) => {
  return window.location.origin + '/#/share/' + sessionShareId;
};

// Function to handle sharing chat
const onClickShareChat = () => {
  // Ask user if they want to share the chat
  $q.dialog({
    title: 'Share Chat',
    message: 'Do you want to share this chat?',
    ok: 'Yes',
    cancel: 'No',
  }).onOk(() => {
    void (async () => {
      // Call the API to share the chat
      const response = await api.post('/chat-session-sharing', {
        session_id: sessionId.value,
        session_name:
          selectedSessionId.value === 'latest'
            ? chatbots.value[0]?.sessions[0]?.session_name || 'New Session'
            : chatbots.value[0]?.sessions.find((s) => s.session_id === selectedSessionId.value)
                ?.session_name || 'New Session',
        course_title: courseTitle.value,
        module_title: moduleTitle.value,
        chatbot_id: globalChatbotId.value,
        chatbot_name: chatbots.value[0]?.chatbot_name || 'Unknown Chatbot',
        conversation: apiConversation.value,
      });
      // Copy the share link to the client's clipboard
      await navigator.clipboard.writeText(getSessionShareLink(response.data.session_sharing_id));
      // Tell the user that the chat has been shared and the link to access it
      $q.notify({
        type: 'positive',
        message: 'The share link has been copied to your clipboard.',
      });
      $q.dialog({
        title: 'Chat Shared',
        message: 'The chat has been shared. The share link has been copied to your clipboard: ',
        prompt: {
          model: getSessionShareLink(response.data.session_sharing_id),
          type: 'text',
          dense: true,
          readonly: true,
        },
        ok: 'OK',
      });
    })();
  });
};

const fetchUserInfo = async () => {
  // Fetch user information
  const response = await api.get('/user-info');
  // console.log(response.data);
  // Update the user information
  user.value = response.data;
};

const urlToHomepage = (role: string) => {
  if (role === 'Teacher') {
    return '/teacher/homepage';
  } else if (role === 'Student') {
    return '/student/homepage';
  } else {
    return '/';
  }
};

const fetchChatbotSessionList = async () => {
  // Fetch chatbot list
  const response = await api.get('/chatbot-usage-session-list', {
    params: {
      student_id: studentId.value,
      chatbot_id: globalChatbotId.value,
      module_id: moduleId.value,
    },
  });
  // console.log(response.data);
  // Update the chatbots with the fetched data
  if (chatbots?.value?.[0] && response?.data) {
    chatbots.value[0].sessions = response.data;
  }
  chatbots.value[0]?.sessions.sort((a, b) => b.session_index - a.session_index);
};

// Watch for changes to session to fetch new internal messages
watch(sessionId, () => {
  if (sessionId.value && sessionId.value !== 'latest') {
    void fetchInternalMessages();
  }
});

// Periodically check for new internal messages
let messagePollingInterval: NodeJS.Timeout | null = null;

onMounted(async () => {
  isFetching.value = true;

  sessionId.value = getStringParam(route.params.sessionId || '');
  selectedSessionId.value = sessionId.value;

  globalChatbotId.value = getStringParam(route.params.chatbotId || '');
  if (chatbots?.value?.[0]) {
    chatbots.value[0].chatbot_id = getStringParam(route.params?.chatbotId || '');
  }

  studentId.value = getStringParam(route.params.studentId || '');

  const chatbotNameValue = route.query.chatbotName as string;
  if (chatbots?.value?.[0]) {
    chatbots.value[0].chatbot_name = chatbotNameValue || 'Unknown Chatbot';
  }

  const courseIdValue = route.query.courseId as string;
  courseId.value = courseIdValue || '';

  const courseTitleValue = route.query.courseTitle as string;
  courseTitle.value = courseTitleValue || 'Unknown Course';

  const moduleIdValue = route.query.moduleId as string;
  moduleId.value = moduleIdValue || '';

  const moduleTitleValue = route.query.moduleTitle as string;
  moduleTitle.value = moduleTitleValue || 'Unknown Module';

  // Fetch user information
  await fetchUserInfo();

  // Fetch chatbot session list
  await fetchChatbotSessionList();

  // Load chatbot details
  await loadChatbotDetails();

  // Load session description if not "latest"
  if (sessionId.value !== 'latest') {
    await loadChatSessionDescription();
  }

  // Fetch internal messages
  if (sessionId.value && sessionId.value !== 'latest') {
    await fetchInternalMessages();
  }

  // Continue the latest session
  if (sessionId.value === 'latest') {
    for (const chatbot of chatbots.value) {
      if (chatbot.chatbot_id === globalChatbotId.value) {
        if (chatbot.sessions.length === 0) {
          $q.notify({
            type: 'negative',
            message: 'This chatbot has no available sessions.',
          });
          break;
        }
        await selectSession(
          globalChatbotId.value,
          studentId.value,
          chatbot.sessions[0]?.session_id || '',
          chatbot.sessions[0]?.session_name || '',
        );
        break;
      }
    }
  }

  // Poll for new messages every 5 minutes
  messagePollingInterval = setInterval(() => {
    if (sessionId.value && sessionId.value !== 'latest') {
      void fetchInternalMessages();
    }
  }, 300000);

  isFetching.value = false;
});

onUnmounted(() => {
  if (messagePollingInterval) {
    clearInterval(messagePollingInterval);
  }
});

watch(route, async (newRoute) => {
  sessionId.value = getStringParam(newRoute.params.sessionId || '');
  selectedSessionId.value = sessionId.value;

  globalChatbotId.value = getStringParam(newRoute.params.chatbotId || '');
  if (chatbots?.value?.[0]) {
    chatbots.value[0].chatbot_id = getStringParam(newRoute.params?.chatbotId || '');
  }

  studentId.value = getStringParam(newRoute.params.studentId || '');

  const chatbotNameValue = newRoute.query.chatbotName as string;
  if (chatbots?.value?.[0]) {
    chatbots.value[0].chatbot_name = chatbotNameValue || 'Unknown Chatbot';
  }

  const courseIdValue = newRoute.query.courseId as string;
  courseId.value = courseIdValue || '';

  const courseTitleValue = newRoute.query.courseTitle as string;
  courseTitle.value = courseTitleValue || 'Unknown Course';

  const moduleIdValue = newRoute.query.moduleId as string;
  moduleId.value = moduleIdValue || '';

  const moduleTitleValue = newRoute.query.moduleTitle as string;
  moduleTitle.value = moduleTitleValue || 'Unknown Module';

  // Load chatbot details
  await loadChatbotDetails();

  // Load session description
  if (sessionId.value !== 'latest') {
    await loadChatSessionDescription();
  }
});

const selectSession = async (
  chatbotId: string,
  studentId: string,
  sessionId: string,
  sessionName: string,
) => {
  selectedSessionId.value = sessionId;
  globalChatbotId.value = chatbotId;
  await router.push({
    path: `/teacher/chatbot/${chatbotId}/usage/${studentId}/session/${sessionId}`,
    query: {
      sessionName: sessionName,
      chatbotName: chatbots.value[0]?.chatbot_name,
      courseId: courseId.value,
      courseTitle: courseTitle.value,
      moduleId: moduleId.value,
      moduleTitle: moduleTitle.value,
    },
  });
};

// Define updating method for apiConversation
const updateApiConversation = (conversation: { role: string; content: string }[]) => {
  apiConversation.value = conversation;
};

// Provide apiConversation update method to child components
provide('updateApiConversation', updateApiConversation);

// Consultation mailbox functions
const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};

const fetchInternalMessages = async () => {
  try {
    const response = await api.get('/internal-messages', {
      params: {
        session_id: sessionId.value,
      },
    });
    internalMessages.value = response.data;

    // Count unread messages for current user
    unreadInternalMessagesCount.value = internalMessages.value.filter(
      (msg) => !msg.is_read && msg.sender_role !== user.value.role_name,
    ).length;

    // Mark messages as read when opening mailbox
    if (showConsultationMailbox.value) {
      await markMessagesAsRead();
    }
  } catch (error) {
    console.error('Error fetching internal messages:', error);
  }
};

const markMessagesAsRead = async () => {
  try {
    await api.put('/internal-messages/mark-read', {
      session_id: sessionId.value,
      user_role: user.value.role_name,
    });
    unreadInternalMessagesCount.value = 0;
  } catch (error) {
    console.error('Error marking messages as read:', error);
  }
};

const openConsultationMailbox = async () => {
  showConsultationMailbox.value = true;
  await fetchInternalMessages();
  await markMessagesAsRead();

  // Scroll to bottom after opening
  await nextTick(() => {
    scrollMessagesToBottom();
  });
};

const scrollMessagesToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const sendInternalMessage = async () => {
  if (!newInternalMessage.value.trim()) return;

  sendingInternalMessage.value = true;

  try {
    // Determine recipient based on current user role
    const recipientRole = user.value.role_name === 'Student' ? 'Teacher' : 'Student';

    await api.post('/internal-messages', {
      session_id: sessionId.value,
      content: newInternalMessage.value.trim(),
      recipient_role: recipientRole,
    });

    newInternalMessage.value = '';
    await fetchInternalMessages();

    // Scroll to bottom after sending
    await nextTick(() => {
      scrollMessagesToBottom();
    });

    $q.notify({
      type: 'positive',
      message:
        user.value.role_name === 'Student' ? 'Message sent to teacher' : 'Reply sent to student',
    });

    // Note: Teacher reply notifications are automatically created by the backend
    // when a teacher sends an internal message to a student
  } catch (error) {
    console.error('Error sending internal message:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to send message',
    });
  } finally {
    sendingInternalMessage.value = false;
  }
};
</script>

<style scoped>
.q-drawer {
  width: 300px;
}

.youtube-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.youtube-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
</style>

<style>
.sessionsummary-container h1 {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.5rem;
  margin: 1.5rem 0;
}

.sessionsummary-container h2 {
  font-size: 22px;
  font-weight: bold;
  line-height: 1.4rem;
  margin: 1.4rem 0;
}

.sessionsummary-container h3 {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3rem;
  margin: 1.3rem 0;
}

.sessionsummary-container h4 {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.2rem;
  margin: 1.2rem 0;
}

.sessionsummary-container h5 {
  font-size: 16px;
  font-weight: bold;
  line-height: 1.1rem;
  margin: 1.1rem 0;
}

.sessionsummary-container h6 {
  font-size: 14px;
  font-weight: bold;
  line-height: 1rem;
  margin: 1rem 0;
}
</style>
