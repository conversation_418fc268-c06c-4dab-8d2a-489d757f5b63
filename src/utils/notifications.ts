import type { QVueGlobals } from 'quasar';

const onNotify = ($q: QVueGlobals) => {
  // Define the start and end timestamps for the system maintenance
  const startTimestamp = new Date('2024-10-28T22:00:00').getTime();
  const endTimestamp = new Date('2024-10-29T07:00:00').getTime();

  // Get the current timestamp
  const currentTimestamp = new Date().getTime();

  // If the current timestamp is before the end timestamp
  if (currentTimestamp < endTimestamp) {
    // Calculate the time until maintenance
    const timeUntilMaintenance = startTimestamp - currentTimestamp;

    // If the current timestamp is after the start timestamp
    if (currentTimestamp >= startTimestamp) {
      // The system maintenance is ongoing
      $q.notify({
        type: 'negative',
        timeout: 10000,
        message: `Service Suspension: System maintenance is ongoing. Service will be restored at ${new Date(endTimestamp).toLocaleString()}.`,
      });
    } else if (timeUntilMaintenance > 0) {
      // The system maintenance is upcoming
      $q.notify({
        type: 'warning',
        timeout: 10000,
        message: `Upcoming Maintenance: Please be aware that system maintenance is scheduled from ${new Date(startTimestamp).toLocaleString()} to ${new Date(endTimestamp).toLocaleString()}. You may experience service interruptions during this time.`,
      });
    }
  }
  // If the current timestamp is after the end timestamp, the system maintenance is over, and no notification is displayed
};

export { onNotify };
