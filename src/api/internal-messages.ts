// Internal messaging API endpoints
// These would need to be implemented in the backend

/**
 * API Endpoints for Internal Messaging System:
 *
 * GET /internal-messages?session_id={sessionId}
 * - Fetch all internal messages for a specific session
 * - Returns: Array<InternalMessage>
 *
 * GET /internal-messages/unsent-to-chatbot?session_id={sessionId}
 * - Fetch internal messages that haven't been sent to chatbot for a specific session
 * - Used when students send new messages to automatically include teacher-student discussions
 * - Returns: Array<InternalMessage>
 *
 * POST /internal-messages
 * - Send a new internal message
 * - Body: { session_id, content, recipient_role }
 * - Returns: { message_id, created_at }
 *
 * PUT /internal-messages/mark-read
 * - Mark internal messages as read for a specific user in a session
 * - Body: { session_id, user_role }
 * - Updates: is_read = true for messages where receiver_user_id = user_id
 * - Returns: { success: boolean, updated_count }
 *
 * PUT /internal-messages/mark-all-sent-to-chatbot?session_id={sessionId}
 * - Mark all internal messages in a session as sent to chatbot
 * - Called automatically when student sends a message that includes teacher-student discussions
 * - Updates: is_sent_to_chatbot = true for all messages in the session
 * - Returns: { success: boolean, updated_count }
 *
 * POST /internal-message-notification
 * - Create a notification for teacher when student raises hand
 * - Body: { session_id, session_name, course_id, course_title, module_id, module_title, chatbot_id }
 * - Returns: { notification_id }
 */

export interface InternalMessage {
  message_id: string; // uuid, PK - 消息识别码
  session_id: string; // uuid, FK - 聊天机器人会话识别码
  sender_user_id: string; // uuid, FK - 发送者用户识别码
  receiver_user_id: string; // uuid, FK - 接收者用户识别码
  message_content: string; // text - 消息内容
  message_type: string; // varchar - 消息类型 ['message']
  is_read: boolean; // bool - 是否已读
  is_sent_to_chatbot: boolean; // bool - 是否已发送给AI

  // Additional fields for frontend display (derived from joins)
  sender_role?: 'Student' | 'Teacher'; // Derived from users table
  receiver_role?: 'Student' | 'Teacher'; // Derived from users table
  created_at?: string; // System timestamp
}

export interface InternalMessageNotification {
  notification_id: string; // uuid, PK - 通知识别码
  course_id: string; // uuid, FK - 课程识别码
  creator_user_id: string; // uuid, FK - 通知创建用户识别码
  notification_title: string; // varchar - 通知标题
  description: {
    // json - 通知详情 (for raise hand notifications)
    type: 'raise_hand';
    session_id: string;
    session_name: string;
    module_id: string;
    module_title: string;
    chatbot_id: string;
    student_user_id: string;
    student_name: string;
    message_content: string; // First message content preview
  };

  // Additional fields for frontend display
  created_at?: string; // System timestamp
}
