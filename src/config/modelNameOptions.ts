// Define the options for the model name dropdown
export const MODEL_NAME_OPTIONS = [
  'OpenAI (o3-mini-high)',
  'OpenAI (o1-mini)',
  'OpenAI (gpt-4.1)',
  'OpenAI (gpt-4.1-mini)',
  'OpenAI (gpt-4o)',
  'OpenAI (gpt-4o-mini)',
  'OpenAI (gpt-4-turbo)',
  'Anthropic Claude 3.5 (claude-3.5-sonnet)',
  'Anthropic Claude 3.5 (claude-3.5-haiku)',
  'Anthropic Claude 3 (claude-3-opus)',
  'DeepSeek R1 (deepseek-r1)',
  'DeepSeek V3 (deepseek-chat)',
  'Qwen2.5 (qwen-2.5-coder-32b-instruct)',
  'Qwen2.5 (qwen-2.5-72b-instruct)',
  'MiniMax-01 (minimax-01)',
  'xAI Grok 2 (grok-2-1212)',
  'Google Gemini (gemini-pro-1.5)',
  'Google Gemini (gemini-pro)',
  'Meta Llama 3.3 (llama-3.3-70b-instruct)',
  'Meta Llama 3.2 (llama-3.2-3b-instruct)',
  'Meta Llama 3.2 (llama-3.2-90b-vision-instruct)',
  'Perplexity Llama 3.1 Online (llama-3.1-sonar-large-128k-online)',
  'Azure Dall-E 3 (dall-e-3-1024)',
];
