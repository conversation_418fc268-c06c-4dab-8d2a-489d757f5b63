<template>
  <q-layout view="hHh lpR fFf">
    <!-- Top Bar -->
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title class="text-weight-bold">
          <q-btn
            flat
            no-caps
            label="Bytewise"
            to="/start"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 0"
          />
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container>
      <q-page v-if="!isFetching">
        <div class="q-banner text-h6 q-pa-md row justify-between">
          <div>{{ courseTitle }} - {{ moduleTitle }} - {{ chatbotName }} - {{ sessionName }}</div>
          <q-space />
        </div>
        <div class="row justify-center">
          <div class="conversation-wrapper col-12 col-sm-10 col-md-8 col-lg-6">
            <div class="row q-mt-md q-pa-md conversation-container" ref="conversationContainer">
              <div class="col-12">
                <div v-for="message in conversation" :key="message.id" class="q-mb-md">
                  <q-chat-message
                    :name="message.name"
                    :avatar="message.avatar"
                    :sent="message.sent"
                    :text-color="message.textColor"
                    :bg-color="message.bgColor"
                  >
                    <div v-html="markdown.render(message.text)"></div>
                  </q-chat-message>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
//   import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import { convertApiConversationToConversation } from 'src/utils/session';
import MarkdownIt from 'markdown-it';
import MarkdownItKatex from 'markdown-it-katex';
const markdown = new MarkdownIt();
markdown.use(MarkdownItKatex);

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

//   const $q = useQuasar();
//   const router = useRouter();
const route = useRoute();

const isFetching = ref(true);
const sessionSharingId = ref<string>('');
const sessionName = ref<string>('');
const courseTitle = ref<string>('');
const moduleTitle = ref<string>('');
const chatbotId = ref<string>('');
const chatbotName = ref<string>('');

// The conversation array contains messages that will be displayed in the chat
const conversation = ref([
  {
    id: 1,
    text: 'Hello! How can I help you today?',
    name: 'Chatbot',
    avatar: 'avatar/chatbot.png',
    sent: false,
    textColor: 'black',
    bgColor: 'grey-4',
  },
]);

// The apiConversation array contains messages that will be sent to the server
const apiConversation = ref([
  {
    role: 'system',
    content: 'Hello! How can I help you today?',
  },
  {
    role: 'assistant',
    content: 'Hello! How can I help you today?',
  },
]);

const fetchSessionSharingInfo = async (sessionSharingIdValue: string) => {
  const response = await api.get('/chat-session-sharing', {
    params: {
      session_sharing_id: sessionSharingIdValue,
    },
  });
  const data = response.data;

  sessionName.value = data.session_name;
  courseTitle.value = data.course_title;
  moduleTitle.value = data.module_title;
  chatbotId.value = data.chatbot_id;
  chatbotName.value = data.chatbot_name;

  // Handle session conversation
  apiConversation.value = data.conversation;
  conversation.value = convertApiConversationToConversation(apiConversation.value);
};

onMounted(() => {
  isFetching.value = true;

  sessionSharingId.value = getStringParam(route.params.sessionSharingId || '');

  fetchSessionSharingInfo(sessionSharingId.value)
    .finally(() => {
      isFetching.value = false;
    })
    .catch(() => {});
});
</script>

<style scoped>
.q-banner {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.conversation-wrapper {
  transition: height 0.3s ease;
}

.conversation-container {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  flex: 1;
  position: relative;
  margin-top: 0;
}

.input-wrapper {
  width: 100%;
  padding: 0 16px;
  position: relative;
  z-index: 1;
}

.input-container {
  border: 1px solid #e0e0e0;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.q-input {
  transition: all 0.3s ease;
}

.conversation-container pre {
  max-width: 100%;
  overflow-x: auto;
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
  position: relative;
}

.conversation-container pre .copy-button {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  padding: 0.25rem;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-container pre .copy-button i {
  font-size: 1rem;
  color: #666;
}

.conversation-container pre:hover .copy-button {
  opacity: 1;
}

.conversation-container pre .copy-button:hover {
  background: rgba(255, 255, 255, 1);
}

.conversation-container pre .copy-button:active {
  transform: scale(0.95);
}
</style>

<style>
.conversation-container h1 {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.5rem;
  margin: 1.5rem 0;
}

.conversation-container h2 {
  font-size: 22px;
  font-weight: bold;
  line-height: 1.4rem;
  margin: 1.4rem 0;
}

.conversation-container h3 {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3rem;
  margin: 1.3rem 0;
}

.conversation-container h4 {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.2rem;
  margin: 1.2rem 0;
}

.conversation-container h5 {
  font-size: 16px;
  font-weight: bold;
  line-height: 1.1rem;
  margin: 1.1rem 0;
}

.conversation-container h6 {
  font-size: 14px;
  font-weight: bold;
  line-height: 1rem;
  margin: 1rem 0;
}

/* Add these new styles for code blocks */
.conversation-container pre {
  max-width: 100%;
  overflow-x: auto;
  background: rgba(0, 0, 0, 0.15);
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
}

.conversation-container code {
  font-family: monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* Style for inline code */
.conversation-container p code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
}
</style>
