<template>
  <q-page>
    <div class="q-banner text-h6 q-pa-md row justify-between q-gutter-sm">
      <q-btn flat dense round icon="arrow_back" to="/teacher/homepage">
        <q-tooltip>Back to Homepage</q-tooltip>
      </q-btn>
      <div>Avatars</div>
      <q-space />
    </div>

    <!-- Avatar Video Container -->
    <div class="row justify-center">
      <div class="avatar-wrapper col-12 col-sm-10 col-md-8 col-lg-6">
        <template v-if="stream">
          <div class="relative">
            <video
              ref="mediaStreamRef"
              autoplay
              playsinline
              muted
              style="width: 100%; height: 100%; object-fit: cover; background: black"
            >
              <track kind="captions" />
            </video>
          </div>

          <!-- Control Buttons -->
          <div class="absolute-bottom-right q-pa-md">
            <div class="column q-gutter-y-sm">
              <q-btn color="primary" label="Interrupt" @click="handleInterrupt" />
              <q-btn color="primary" label="End Session" @click="endSession" />
            </div>
          </div>
        </template>

        <!-- <template v-else-if="!isLoadingSession"> -->
        <!-- Session Setup Form -->
        <!-- <div class="column q-pa-lg "> -->
        <!-- <q-input
              v-model="knowledgeBase"
              label="Knowledge Base"
              outlined
              placeholder="Enter a custom knowledge base"
              disable
            />
            <q-select
              v-model="selectedAvatar"
              :options="avatarOptions"
              label="Select Avatar"
              outlined
              disable
            />
            <div class="row q-gutter-sm">
              <q-select
                v-model="selectedVoice"
                :options="voiceOptions"
                label="Select Voice"
                outlined
                class="col"
                disable
              />
              <q-btn
                v-if="selectedVoice"
                icon="play_arrow"
                flat
                color="primary"
                @click="previewVoice"
                disable
              >
                <q-tooltip>Preview Voice</q-tooltip>
              </q-btn>
            </div>

            <q-select
              v-model="language"
              :options="languageOptions"
              label="Select Language"
              outlined
              disable
            /> -->

        <!-- <q-btn
              color="primary"
              label="Start Session"
              class="full-width"
              @click="startSession"
              :loading="isLoadingSession"
            />
          </div>
        </template> -->
        <template v-else-if="!isLoadingSession">
          <!-- Session Setup Form -->
          <div class="column q-pa-lg">
            <q-btn
              color="primary"
              label="Start Session"
              class="full-width"
              @click="startSession"
              :loading="isLoadingSession"
            />
          </div>
        </template>
        <template v-else>
          <div class="full-height column justify-center items-center">
            <q-spinner size="3em" />
          </div>
        </template>
      </div>
    </div>

    <!-- Chat Controls -->
    <div class="row justify-center q-mt-md">
      <div class="col-12 col-sm-10 col-md-8 col-lg-6">
        <q-tabs v-model="chatMode" class="text-primary">
          <q-tab name="text_mode" label="Text Mode" />
          <q-tab name="voice_mode" label="Voice Mode" />
        </q-tabs>

        <div class="q-pa-md">
          <template v-if="chatMode === 'text_mode'">
            <div class="row items-center q-gutter-sm">
              <q-input
                v-model="text"
                outlined
                class="col"
                placeholder="Type something for the avatar to respond"
                :disable="!stream"
              />
              <q-btn
                color="primary"
                icon="send"
                round
                :loading="isLoadingRepeat"
                @click="handleSpeak"
                :disable="!stream"
              />
            </div>
          </template>
          <template v-else>
            <div class="text-center">
              <q-btn
                color="primary"
                :label="isUserTalking ? 'Listening...' : 'Voice Chat'"
                :disable="!isUserTalking"
              />
            </div>
          </template>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onUnmounted, watch, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import StreamingAvatar, {
  AvatarQuality,
  StreamingEvents,
  TaskMode,
  TaskType,
  VoiceEmotion,
} from '@heygen/streaming-avatar';
import { useRoute } from 'vue-router';
import { api } from 'boot/axios';
const $q = useQuasar();

// Define an interface for the avatar object
interface AvatarData {
  avatar_id: string;
  created_at: number;
  is_public: boolean;
  status: string;
}

// Define an interface for avatar options
interface AvatarOption {
  label: string;
  value: string;
  voiceId: string;
}

// Define an interface for voice data
interface VoiceData {
  voice_id: string;
  language: string;
  gender: string;
  name: string;
  preview_audio: string;
  support_pause: boolean;
  emotion_support: boolean;
  support_interactive_avatar: boolean;
}

// Define an interface for voice options
interface VoiceOption {
  label: string;
  value: string;
  language: string;
  preview_audio?: string;
}

// State
const isLoadingSession = ref(false);
const isLoadingRepeat = ref(false);
const stream = ref<MediaStream>();
const debug = ref('');
const selectedAvatar = ref<AvatarOption | null>(null);
const language = ref('en');
const chatMode = ref('text_mode');
const isUserTalking = ref(false);
const text = ref('');
const mediaStreamRef = ref<HTMLVideoElement | null>(null);
const avatar = ref<StreamingAvatar | null>(null);
const avatarOptions = ref<AvatarOption[]>([]);
const knowledgeBase = ref('');
const selectedVoice = ref<VoiceOption | null>(null);
const voiceOptions = ref<VoiceOption[]>([]);

const route = useRoute();
// Constants
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const languageOptions = [{ label: 'English', value: 'en' }];

// Add type definition
interface AudioContextType {
  AudioContext: typeof AudioContext;
  webkitAudioContext: typeof AudioContext;
}

// Methods
const fetchAccessToken = async () => {
  try {
    const apiKey = import.meta.env.VITE_HEYGEN_API_TOKEN;
    if (!apiKey) {
      throw new Error('HEYGEN_API_TOKEN not found in environment variables');
    }

    // Get streaming session token
    const response = await fetch('https://api.heygen.com/v1/streaming.create_token', {
      method: 'POST',
      headers: {
        'X-Api-Key': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({}), // Empty object as request body
    });

    if (!response.ok) {
      throw new Error('Failed to create streaming token');
    }

    const data = await response.json();
    if (data.error) {
      throw new Error(data.error);
    }

    return data.data.token; // Return streaming session token
  } catch (error) {
    console.error('Error getting access token:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to get access token',
    });
    return '';
  }
};

const startSession = async () => {
  if (!selectedAvatar.value || !selectedVoice.value) {
    $q.notify({
      type: 'negative',
      message: 'Please select both an avatar and a voice',
    });
    return;
  }

  isLoadingSession.value = true;
  try {
    // Initialize audio context
    await initAudioContext();

    const token = await fetchAccessToken();
    avatar.value = new StreamingAvatar({ token });
    setupAvatarEventListeners();
    console.log(selectedAvatar.value.value);
    console.log(selectedVoice.value.value);
    await avatar.value.createStartAvatar({
      avatarName: selectedAvatar.value.value,
      quality: AvatarQuality.Low,
      knowledgeBase: knowledgeBase.value,
      voice: {
        voiceId: selectedVoice.value.value,
        rate: 1,
        emotion: VoiceEmotion.EXCITED,
      },
      language: language.value,
      disableIdleTimeout: true,
    });

    await avatar.value.startVoiceChat({
      useSilencePrompt: false,
    });
    chatMode.value = 'voice_mode';
  } catch (error) {
    console.error('Error starting avatar session:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to start avatar session',
    });
  } finally {
    isLoadingSession.value = false;
  }
};

const setupAvatarEventListeners = () => {
  if (!avatar.value) return;

  avatar.value.on(StreamingEvents.STREAM_READY, (event) => {
    void (async () => {
      console.log('Stream ready event:', event);
      console.log('Stream detail:', event.detail);

      // Check media stream tracks
      const tracks = event.detail.getTracks();
      console.log('Media tracks:', tracks);

      const videoTracks = event.detail.getVideoTracks();
      const audioTracks = event.detail.getAudioTracks();
      console.log('Video tracks:', videoTracks);
      console.log('Audio tracks:', audioTracks);

      if (videoTracks.length === 0 || audioTracks.length === 0) {
        console.error('Missing media tracks:', {
          videoTracks: videoTracks.length,
          audioTracks: audioTracks.length,
        });
        return;
      }

      // Ensure all tracks are enabled
      tracks.forEach((track: MediaStreamTrack) => {
        track.enabled = true;
        console.log(`Track ${track.kind} enabled:`, track.enabled);
      });

      stream.value = event.detail;

      // Wait for a short time before setting media stream
      await new Promise((resolve) => setTimeout(resolve, 100));

      if (mediaStreamRef.value) {
        try {
          // Remove old media stream
          if (mediaStreamRef.value.srcObject) {
            const oldStream = mediaStreamRef.value.srcObject as MediaStream;
            oldStream.getTracks().forEach((track) => track.stop());
          }
          mediaStreamRef.value.srcObject = null;

          // Set new media stream
          mediaStreamRef.value.srcObject = event.detail;
          mediaStreamRef.value.muted = true; // Set to mute first

          // Wait for media to load
          await new Promise((resolve) => {
            if (!mediaStreamRef.value) return;
            mediaStreamRef.value.onloadedmetadata = resolve;
            mediaStreamRef.value.onloadeddata = resolve;
          });

          // Try to play
          await mediaStreamRef.value.play();
          console.log('Video started playing from STREAM_READY event');

          // Unmute
          setTimeout(() => {
            if (mediaStreamRef.value) {
              mediaStreamRef.value.muted = false;
              mediaStreamRef.value.volume = 1.0;
              console.log('Unmuted video element');
            }
          }, 500);
        } catch (error) {
          console.error('Error setting up video:', error);
        }
      }
    })();
  });

  avatar.value.on(StreamingEvents.AVATAR_START_TALKING, (e) => {
    console.log('Avatar started talking', e);
    // Check video element state
    if (mediaStreamRef.value) {
      console.log('Video element state:', {
        readyState: mediaStreamRef.value.readyState,
        paused: mediaStreamRef.value.paused,
        currentTime: mediaStreamRef.value.currentTime,
        videoWidth: mediaStreamRef.value.videoWidth,
        videoHeight: mediaStreamRef.value.videoHeight,
        muted: mediaStreamRef.value.muted,
        volume: mediaStreamRef.value.volume,
      });
    }
  });

  avatar.value.on(StreamingEvents.AVATAR_STOP_TALKING, (e) => {
    console.log('Avatar stopped talking', e);
  });

  avatar.value.on(StreamingEvents.STREAM_DISCONNECTED, () => {
    void (async () => {
      console.log('Stream disconnected');
      await endSession();
    })();
  });

  avatar.value.on(StreamingEvents.USER_START, () => {
    isUserTalking.value = true;
  });

  avatar.value.on(StreamingEvents.USER_STOP, () => {
    isUserTalking.value = false;
  });
};

const handleSpeak = async () => {
  if (!avatar.value || !text.value) return;

  isLoadingRepeat.value = true;
  try {
    await avatar.value.speak({
      text: text.value,
      taskType: TaskType.REPEAT,
      taskMode: TaskMode.SYNC,
    });
  } catch (error: unknown) {
    if (error instanceof Error) {
      debug.value = error.message;
    } else {
      debug.value = 'An unknown error occurred';
    }
    $q.notify({
      type: 'negative',
      message: 'Failed to speak',
    });
  } finally {
    isLoadingRepeat.value = false;
  }
};

const handleInterrupt = async () => {
  try {
    await avatar.value?.interrupt();
  } catch (error: unknown) {
    if (error instanceof Error) {
      debug.value = error.message;
    } else {
      debug.value = 'An unknown error occurred';
    }
  }
};

const endSession = async () => {
  await avatar.value?.stopAvatar();
  stream.value = undefined;
};

// Watchers
watch(stream, async (newStream) => {
  if (newStream && mediaStreamRef.value) {
    try {
      console.log('Setting up new media stream...');

      // Check media stream state
      const videoTracks = newStream.getVideoTracks();
      const audioTracks = newStream.getAudioTracks();
      console.log('Video tracks:', videoTracks);
      console.log('Audio tracks:', audioTracks);

      // Ensure all tracks are enabled
      videoTracks.forEach((track: MediaStreamTrack) => {
        track.enabled = true;
        console.log('Video track enabled:', track.enabled);
      });
      audioTracks.forEach((track: MediaStreamTrack) => {
        track.enabled = true;
        console.log('Audio track enabled:', track.enabled);
      });

      // Reset media stream
      mediaStreamRef.value.srcObject = null;
      mediaStreamRef.value.srcObject = newStream;

      // Wait for media to load
      await new Promise((resolve) => {
        if (mediaStreamRef.value) {
          mediaStreamRef.value.onloadedmetadata = () => {
            console.log('Metadata loaded');
            resolve(null);
          };
          // Add timeout handling
          setTimeout(resolve, 3000);
        }
      });

      // Try to play
      console.log('Attempting to play...');
      await mediaStreamRef.value.play();
      console.log('Video started playing');

      // Immediately unmute
      mediaStreamRef.value.muted = false;
      mediaStreamRef.value.volume = 1.0;
      console.log('Unmuted video element');

      debug.value = 'Playing';
    } catch (error) {
      console.error('Error playing video:', error);
      debug.value = 'Failed to play video';
      $q.notify({
        type: 'negative',
        message: 'Failed to play media stream',
      });
    }
  }
});

watch(chatMode, async (newMode, oldMode) => {
  if (newMode === oldMode) return;

  if (newMode === 'text_mode') {
    avatar.value?.closeVoiceChat();
  } else {
    await avatar.value?.startVoiceChat();
  }
});

onUnmounted(async () => {
  await endSession();
});

// Fetch avatars from API
const fetchAvatars = async () => {
  try {
    const response = await fetch('https://api.heygen.com/v1/streaming/avatar.list', {
      method: 'GET',
      headers: {
        'X-Api-Key': import.meta.env.VITE_HEYGEN_API_TOKEN,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch avatars');
    }

    const responseData = await response.json();

    if (responseData.code === 100 && Array.isArray(responseData.data)) {
      avatarOptions.value = responseData.data.map((avatar: AvatarData) => ({
        label: avatar.avatar_id,
        value: avatar.avatar_id,
        voiceId: selectedVoice.value?.value,
      }));
      // If no avatar is selected, select the first avatar
      if (!selectedAvatar.value && avatarOptions.value.length > 0) {
        selectedAvatar.value = avatarOptions.value[84] ?? null;
      }
    } else {
      console.error('Invalid response format or no avatars found');
      $q.notify({
        type: 'negative',
        message: 'No avatars found',
      });
    }
  } catch (error) {
    console.error('Error fetching avatars:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to load avatars',
    });
  }
};

// Fetch voices from API
const fetchVoices = async () => {
  try {
    const response = await fetch('https://api.heygen.com/v2/voices', {
      method: 'GET',
      headers: {
        'X-Api-Key': import.meta.env.VITE_HEYGEN_API_TOKEN,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch voices');
    }

    const responseData = await response.json();
    if (responseData.data?.voices && Array.isArray(responseData.data.voices)) {
      voiceOptions.value = responseData.data.voices.map((voice: VoiceData) => ({
        label: `${voice.name} (${voice.gender})`,
        value: voice.voice_id,
        language: voice.language,
        preview_audio: voice.preview_audio,
      }));

      // If no voice is selected, select the first voice
      if (!selectedVoice.value && voiceOptions.value.length > 0) {
        selectedVoice.value = voiceOptions.value[0] ?? null;
      }
    } else {
      console.error('Invalid voice data format:', responseData);
      $q.notify({
        type: 'negative',
        message: 'Failed to load voices: Invalid data format',
      });
    }
  } catch (error) {
    console.error('Error fetching voices:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to load voices',
    });
  }
};

// Fetch avatars when the page is mounted
onMounted(async () => {
  void fetchAvatars();
  void fetchVoices();

  // Add one-time click event listener
  const handleFirstClick = () => {
    void initAudioContext();
    document.removeEventListener('click', handleFirstClick);
  };
  document.addEventListener('click', handleFirstClick);

  // Check media status periodically
  const interval = setInterval(checkMediaStatus, 5000);
  onUnmounted(() => clearInterval(interval));

  await setKnowledgeBase();
});

interface Avatar {
  knowledge_base: string;
}
interface GetKnowledgeBaseResponse {
  success: boolean;
  avatar?: Avatar;
  data?: Avatar[];
  [key: string]: unknown;
}
const setKnowledgeBase = async () => {
  const id = route.query.id;
  const response = await api.get<GetKnowledgeBaseResponse>(
    '/get_avatar_knowledge/' + (Array.isArray(id) ? id[0] : id),
  );

  // Assign the knowledge base value from the response
  knowledgeBase.value = response.data.avatar?.knowledge_base || '';
};
// Add a function to check media status
const checkMediaStatus = () => {
  if (!mediaStreamRef.value || !stream.value) return;

  const videoElement = mediaStreamRef.value;
  const mediaStream = stream.value;

  console.log('Media Status Check:', {
    videoElement: {
      readyState: videoElement.readyState,
      paused: videoElement.paused,
      muted: videoElement.muted,
      volume: videoElement.volume,
      currentTime: videoElement.currentTime,
      videoWidth: videoElement.videoWidth,
      videoHeight: videoElement.videoHeight,
    },
    mediaStream: {
      active: mediaStream.active,
      videoTracks: mediaStream.getVideoTracks().map((track) => ({
        enabled: track.enabled,
        muted: track.muted,
        readyState: track.readyState,
      })),
      audioTracks: mediaStream.getAudioTracks().map((track) => ({
        enabled: track.enabled,
        muted: track.muted,
        readyState: track.readyState,
      })),
    },
  });
};

// Modify initAudioContext function
const initAudioContext = async () => {
  try {
    const AudioContextClass =
      (window as unknown as AudioContextType).AudioContext ||
      (window as unknown as AudioContextType).webkitAudioContext;
    const audioContext = new AudioContextClass();
    if (audioContext.state === 'suspended') {
      await audioContext.resume();
    }
    return audioContext;
  } catch (error) {
    console.error('Failed to initialize audio context:', error);
    return null;
  }
};

// Optimized previewVoice function
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const previewVoice = async () => {
  if (!selectedVoice.value) return;

  try {
    const voiceData = voiceOptions.value.find((v) => v.value === selectedVoice.value?.value);

    if (voiceData?.preview_audio) {
      const audio = new Audio(voiceData.preview_audio);
      await audio.play();
    } else {
      throw new Error('Preview audio not available for the selected voice.');
    }
  } catch (error) {
    console.error('Error playing preview:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to play voice preview',
    });
  }
};
</script>

<style scoped>
.avatar-wrapper {
  position: relative;
  margin: 0 16px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
}
</style>
