<template>
  <q-page class="q-pa-md" v-if="!isFetching">
    <div class="text-h3 q-pa-md">Manage Avatars</div>
    <q-separator spaced />
    <div class="q-gutter-sm q-pa-md">
      <q-btn
        no-caps
        label="Create New Avatar"
        color="black"
        text-color="white"
        @click="handleUploadAvatar"
      />
    </div>
    <q-separator spaced />
    <div class="q-pa-md">
      <div class="text-h6">Avatar List</div>
      <div class="q-py-md">
        <q-input v-model="filter" label="Search avatars by name" clearable type="text" />
      </div>

      <q-table :rows-per-page-options="[10, 20]" :columns="columns" :rows="rows" :filter="filter">
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th auto-width />
            <q-th v-for="col in props.cols" :key="col.name" :props="props">
              {{ col.label }}
            </q-th>
          </q-tr>
        </template>
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td auto-width>
              <q-btn flat dense round icon="edit" @click="handleEditAvatar(props.row)">
                <q-tooltip>Edit</q-tooltip>
              </q-btn>
            </q-td>
            <q-td v-for="col in props.cols" :key="col.name" :props="props">
              {{ col.value }}
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { api } from 'boot/axios';

// ----------------- Type Definitions ----------------- //
interface Avatar {
  id: string | number;
  avatar_name: string;
  knowledge_base: string;
  [key: string]: string | number;
  index?: number;
}

interface GetAvatarListResponse {
  success: boolean;
  data: Avatar[];
  [key: string]: Avatar[] | boolean;
}

// ----------------- Main Logic ----------------- //
const router = useRouter();

const isFetching = ref<boolean>(true);
const filter = ref<string>('');
const rows = ref<Avatar[]>([]);

type Column = {
  name: string;
  required?: boolean;
  label: string;
  align?: 'left' | 'right' | 'center';
  field: (row: Avatar) => number | undefined | string;
  sortable?: boolean;
};

const columns: Column[] = [
  {
    name: 'id',
    required: true,
    label: 'Index',
    align: 'left',
    field: (row: Avatar) => row.index,
    sortable: true,
  },
  {
    name: 'name',
    required: true,
    label: 'Avatar Name',
    align: 'left',
    field: (row: Avatar) => row.avatar_name,
    sortable: true,
  },
  {
    name: 'knowledgeBase',
    label: 'Knowledge Base',
    field: (row: Avatar) => row.knowledge_base,
    sortable: true,
  },
  {
    name: 'uniqueName',
    label: 'Avatar Unique ID',
    field: (row: Avatar) => row.id, // Using id as unique name
    sortable: true,
  },
];

const fetchAvatars = async (): Promise<void> => {
  try {
    isFetching.value = true;
    const response = await api.get<GetAvatarListResponse>('/get_avatar_list');
    const responseData = response.data;
    if (responseData && responseData.success && Array.isArray(responseData.data)) {
      rows.value = responseData.data.map((item, index) => ({
        ...item,
        index: index + 1,
      }));
    } else {
      console.error('Unexpected response format:', responseData);
      rows.value = [];
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error('Error fetching avatars:', error);
    if (error.response) {
      console.error('Backend error:', error.response.data);
    }
    rows.value = [];
  } finally {
    isFetching.value = false;
  }
};

onMounted(async () => {
  await fetchAvatars();
});

const handleUploadAvatar = async (): Promise<void> => {
  await router.push('/teacher/avatar/new');
};

const handleEditAvatar = async (avatar: Avatar): Promise<void> => {
  console.log('Editing avatar:', avatar);

  localStorage.setItem(
    'editAvatarData',
    JSON.stringify({
      avatarId: avatar.id,
      avatarName: avatar.avatar_name,
      knowledgeBase: avatar.knowledge_base,
    }),
  );

  await router.push('/teacher/edit-avatar');
};
</script>
