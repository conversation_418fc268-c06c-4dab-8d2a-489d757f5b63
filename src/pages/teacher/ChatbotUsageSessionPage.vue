<template>
  <q-page v-if="!isFetching">
    <div class="q-banner text-h6 q-pa-md row justify-between">
      <div>{{ moduleTitle }} - {{ selectedChatbot.chatbot_name }} - {{ sessionName }}</div>
      <q-space />
    </div>
    <div class="row justify-center">
      <div class="conversation-wrapper col-12 col-sm-10 col-md-8 col-lg-6">
        <div class="row q-mt-md q-pa-md conversation-container" ref="conversationContainer">
          <div class="col-12">
            <div v-for="message in conversation" :key="message.id" class="q-mb-md">
              <q-chat-message
                :name="message.name"
                :avatar="message.avatar"
                :sent="message.sent"
                :text-color="message.textColor"
                :bg-color="message.bgColor"
              >
                <div v-html="markdown.render(message.text)"></div>
              </q-chat-message>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, inject } from 'vue';
import { useRoute } from 'vue-router';
import { api } from 'boot/axios';
import { convertApiConversationToConversation } from 'src/utils/session';
import MarkdownIt from 'markdown-it';
import MarkdownItKatex from 'markdown-it-katex';

const markdown = new MarkdownIt();
markdown.use(MarkdownItKatex);
const route = useRoute();

// Get the updateApiConversation function from the parent component
const updateApiConversation = inject('updateApiConversation') as (
  conversation: { role: string; content: string }[],
) => void;

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

// The conversation array contains messages that will be displayed in the chat
const conversation = ref([
  {
    id: 1,
    text: 'Hello! How can I help you today?',
    name: 'Chatbot',
    avatar: 'avatar/chatbot.png',
    sent: false,
    textColor: 'black',
    bgColor: 'grey-4',
  },
]);

// The apiConversation array contains messages that will be sent to the server
const apiConversation = ref([
  {
    role: 'system',
    content: 'Hello! How can I help you today?',
  },
  {
    role: 'assistant',
    content: 'Hello! How can I help you today?',
  },
]);

const isFetching = ref(true);
// const isSending = ref(false);

// const newMessage = ref('');

const sessionId = ref('');
const sessionName = ref('');
const moduleTitle = ref('');
const studentId = ref('');

interface Chatbot {
  chatbot_id: string;
  id: number;
  created_at: string;
  chatbot_name: string;
  model_name: string;
  system_prompt: string;
  welcome_prompt: string;
  temperature: number;
  type_name: string;
  description: JSON;
  knowledge_base_persist_directory: string;
  knowledge_base_file_paths: string[];
  knowledge_base_file_names: string[];
  knowledge_base_embedding_model: string;
  updated_at: string;
  deleted_at: string;
}

const selectedChatbot = ref<Chatbot>({
  chatbot_id: '',
  id: 0,
  created_at: '',
  chatbot_name: '',
  model_name: '',
  system_prompt: '',
  welcome_prompt: '',
  temperature: 0,
  type_name: '',
  description: JSON.parse('{}'),
  knowledge_base_persist_directory: '',
  knowledge_base_file_paths: [],
  knowledge_base_file_names: [],
  knowledge_base_embedding_model: '',
  updated_at: '',
  deleted_at: '',
});
const selectedChatbotId = ref('');

const fetchSessionInfo = async (
  sessionIdValue: string,
  selectedChatbotValue: Chatbot,
  studentId: string,
) => {
  // Fetch session information from the server
  const response = await api.get('/chat-session-by-student', {
    params: {
      session_id: sessionIdValue,
      student_id: studentId,
    },
  });
  // console.log(response.data);

  // Handle the conversation data
  // When conversation is null, it means the session is new
  if (!response.data.conversation) {
    // We need to build the apiConversation array from the system prompt and welcome prompt
    apiConversation.value = [
      {
        role: 'system',
        content: selectedChatbotValue.system_prompt,
      },
    ];
    // If the chatbot has a welcome prompt, add it to the apiConversation array
    if (selectedChatbotValue.welcome_prompt !== '') {
      apiConversation.value.push({
        role: 'assistant',
        content: selectedChatbotValue.welcome_prompt,
      });
    }
  } else {
    // When conversation is not null, we need to update the apiConversation array with the fetched data
    apiConversation.value = response.data.conversation;
  }

  // Update parent component's apiConversation
  updateApiConversation(apiConversation.value);

  // Update the conversation with the fetched data
  conversation.value = convertApiConversationToConversation(apiConversation.value);
};

const fetchChatbotInfo = async (selectedChatbotIdValue: string) => {
  // Fetch chatbot information from the server
  const response = await api.get('/chatbot', {
    params: {
      chatbot_id: selectedChatbotIdValue,
    },
  });
  // console.log(response.data);
  // Update the selectedChatbot with the fetched data
  selectedChatbot.value = response.data;
};

const scrollToBottom = () => {
  const container = document.querySelector('.conversation-container');
  if (container) {
    container.scrollTop = container.scrollHeight;
  }
};

// const onClickAttachFile = () => {
//   // Placeholder for attaching files
// };

// const onClickUploadImage = () => {
//   // Placeholder for uploading images
// };

// const onClickRecordVoice = () => {
//   // Placeholder for recording voice
// };

watch(
  route,
  async (newRoute) => {
    isFetching.value = true;

    sessionId.value = getStringParam(newRoute.params.sessionId || '');

    selectedChatbotId.value = getStringParam(newRoute.params.chatbotId || '');

    const sessionNameValue = newRoute.query.sessionName as string;
    sessionName.value = sessionNameValue || 'New Session';

    const moduleTitleValue = newRoute.query.moduleTitle as string;
    moduleTitle.value = moduleTitleValue || 'Unknown Module';

    studentId.value = getStringParam(newRoute.params.studentId || '');

    // Fetch the chatbot information and session information
    await fetchChatbotInfo(selectedChatbotId.value);
    // If the session_id is 'latest', do not fetch the session information
    if (sessionId.value !== 'latest') {
      await fetchSessionInfo(sessionId.value, selectedChatbot.value, studentId.value);
    }
    isFetching.value = false;
  },
  { immediate: true },
);

watch(conversation, async () => {
  await nextTick(() => {
    scrollToBottom();
  });
});
</script>

<style scoped>
.q-banner {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.conversation-wrapper {
  height: calc(100vh - 120px);
  margin: 0 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.conversation-container {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  flex: 1;
  position: relative;
  margin-top: 0;
}
</style>

<style>
/* Add the same markdown styling as SessionPage */
.conversation-container h1 {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.5rem;
  margin: 1.5rem 0;
}

.conversation-container h2 {
  font-size: 22px;
  font-weight: bold;
  line-height: 1.4rem;
  margin: 1.4rem 0;
}

.conversation-container h3 {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3rem;
  margin: 1.3rem 0;
}

.conversation-container h4 {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.2rem;
  margin: 1.2rem 0;
}

.conversation-container h5 {
  font-size: 16px;
  font-weight: bold;
  line-height: 1.1rem;
  margin: 1.1rem 0;
}

.conversation-container h6 {
  font-size: 14px;
  font-weight: bold;
  line-height: 1rem;
  margin: 1rem 0;
}
</style>
