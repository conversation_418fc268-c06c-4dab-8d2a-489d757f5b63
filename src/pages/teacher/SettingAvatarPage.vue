<template>
  <q-page class="q-pa-md">
    <div class="text-h6 q-pa-md row q-gutter-sm">
      <q-btn flat dense round icon="arrow_back" to="/">
        <q-tooltip>Back to Avatars</q-tooltip>
      </q-btn>
      <div>Edit Avatar</div>
    </div>

    <div class="q-pa-md">
      <div class="text-h6">Edit Avatar Information</div>
      <q-card>
        <q-card-section>
          <q-input
            v-model="form.name"
            label="Avatar Name"
            clearable
            type="text"
            :rules="[
              (val) => !!val || 'Avatar Name is required',
              (val) => val.length <= 20 || 'Name must be less than 20 characters',
            ]"
          />

          <q-input
            v-model="form.knowledge"
            label="Knowledge Description"
            clearable
            type="textarea"
            :rules="[
              (val) => !!val || 'Knowledge description is required',
            ]"
          />
        </q-card-section>
      </q-card>
    </div>

    <div class="q-pa-md">
      <q-btn
        no-caps
        label="Update Avatar"
        @click="handleUpdateAvatar"
        color="primary"
        :disable="!isFormValid"
      />
    </div>
    <q-separator spaced />
    <div class="q-pa-md">
      <div class="text-h6">Danger Zone</div>
      <q-btn
        no-caps
        label="Delete Avatar"
        @click="handleDeleteAvatar"
        color="red"
        text-color="white"
      />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import { useRouter } from 'vue-router';

interface AvatarForm {
  id: string;
  name: string;
  knowledge: string;
}

interface EditAvatarData {
  avatarId: string;
  avatarName: string;
  knowledgeBase: string;
}

const router = useRouter();
const $q = useQuasar();

const form = ref<AvatarForm>({
  id: '',
  name: '',
  knowledge: '',
});

const isFormValid = computed((): boolean => {
  return !!(form.value.name && form.value.knowledge);
});

const handleDeleteAvatar = () => {
  try {
    $q.dialog({
      title: 'Delete Avatar',
      message: 'Are you sure you want to delete the avatar?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        await api.delete('/delete_avatar', {
          params: {
            avatar_id: form.value.id,
          },
        });
        $q.notify({
          type: 'positive',
          message: 'Avatar deleted successfully',
        });
        await router.push('/teacher/manage-avatars');
      })();
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to delete the chatbot: ' + String(error),
    });
  }
};

// Load data from localStorage when component mounts
onMounted(() => {
  const item = localStorage.getItem('editAvatarData');
  if (item) {
    try {
      const editAvatarData = JSON.parse(item) as EditAvatarData;
      form.value = {
        id: editAvatarData.avatarId,
        name: editAvatarData.avatarName,
        knowledge: editAvatarData.knowledgeBase,
      };
    } catch (error) {
      console.error('Error parsing avatar data from localStorage:', error);
      $q.notify({
        type: 'negative',
        message: 'Failed to parse avatar data',
        position: 'top',
      });
    }
  } else {
    $q.notify({
      type: 'negative',
      message: 'No avatar data found to edit',
      position: 'top',
    });
  }
});

const handleUpdateAvatar = async (): Promise<void> => {
  try {
    const response = await api.put(`/update_avatar/${form.value.id}`, {
      name: form.value.name,
      knowledge: form.value.knowledge,
    });

    console.log('Server response:', response.data);

    $q.notify({
      type: 'positive',
      message: `Avatar "${form.value.name}" updated successfully!`,
      position: 'top',
    });

    // Clear the localStorage after successful update
    localStorage.removeItem('editAvatarData');
    await router.push('/teacher/manage-avatars');
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error('Error updating avatar:', error?.response?.data || error?.message);

    $q.notify({
      type: 'negative',
      message: error?.response?.data?.message || 'Failed to update avatar',
      position: 'top',
    });
  }
};
</script>
