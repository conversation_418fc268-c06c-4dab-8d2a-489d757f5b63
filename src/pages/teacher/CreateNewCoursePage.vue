<template>
  <q-page class="q-pa-md" v-if="!isFetching">
    <div class="text-h3 q-pa-md">Create New Course</div>
    <div class="q-pa-md q-gutter-sm" v-if="isDisplayStep1">
      <div class="text-h6">Step 1: Create a Student Group</div>
      <div>
        You can create a new Student Group or connect with an existing Student Group below, remember
        to click create or connect when you finish this section.
      </div>
      <q-card>
        <q-card-section class="q-gutter-sm">
          <q-input
            v-model="newStudentGroupName"
            label="New Student Group Name"
            clearable
            type="text"
            :rules="[(val) => !!val || 'Student Group Name is required']"
          />
          <q-input
            v-model="newStudentGroupDesc"
            label="New Student Group Description"
            clearable
            type="text"
          />
          <q-btn
            no-caps
            color="black"
            text-color="white"
            label="Create Student Group"
            @click="handleCreateStudentGroup"
          />
        </q-card-section>
      </q-card>

      <div class="text-h6">Or connect with an existing Student Group</div>
      <q-card>
        <q-card-section class="q-gutter-sm">
          <q-input
            v-model="existingStudentGroupId"
            label="Existing Student Group ID"
            clearable
            type="text"
            :rules="[(val) => !!val || 'Student Group ID is required']"
          />
          <q-btn
            no-caps
            color="black"
            text-color="white"
            label="Connect Student Group"
            @click="handleConnectStudentGroup"
          />
        </q-card-section>
      </q-card>
    </div>
    <div class="q-pa-md" v-if="isDisplayStep2">
      <div class="text-h6">Step 2: Enter Course Information</div>
      <q-card>
        <q-card-section>
          <q-input
            v-model="newCourseTitle"
            label="Course Title"
            clearable
            type="text"
            :rules="[(val) => !!val || 'Course Title is required']"
          />
          <q-input
            v-model="newCourseStartDate"
            label="Course Start Date"
            clearable
            type="date"
            :rules="[(val) => !!val || 'Course Start Date is required']"
          />
          <q-input
            v-model="newCourseEndDate"
            label="Course End Date"
            clearable
            type="date"
            :rules="[(val) => !!val || 'Course End Date is required']"
          />
          <q-btn
            no-caps
            label="Create New Course"
            color="black"
            text-color="white"
            @click="handleCreateCourse"
          />
        </q-card-section>
      </q-card>
    </div>
    <div class="q-pa-md" v-if="isDisplayStep3">
      <div class="text-h6">Step 3: Add Student to the connected Student Group</div>
      <div>Note:</div>
      <div>
        1. The student must have an account in Bytewise, and the email must be the same as the
        student's email.
      </div>
      <div>
        2. The students added here will affect all courses connected with the current student group.
      </div>
      <q-card>
        <q-card-section>
          <q-input
            v-model="email"
            label="Enter student's email to add"
            clearable
            type="email"
            :rules="[
              (val) => !!val || 'Email is required',
              (val) => /.+@.+\..+/.test(val) || 'Email must be valid',
            ]"
            @keyup.enter="handleAddStudent"
          >
            <template v-slot: after>
              <q-btn flat icon=" add" @click="handleAddStudent">
                <q-tooltip>Add Student</q-tooltip>
              </q-btn>
            </template>
          </q-input>
        </q-card-section>
      </q-card>
    </div>
    <div class="q-pa-md q-gutter-sm" v-if="isDisplayStep4">
      <div class="text-h6">Step 4: Manage Students in the connected Student Group</div>
      <div>Note: You can also manage the student group in the course page.</div>
      <q-table :rows-per-page-options="[10, 20]" :columns="columns" :rows="rows">
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th auto-width />
            <q-th v-for="col in props.cols" :key="col.name" :props="props">
              {{ col.label }}
            </q-th>
          </q-tr>
        </template>
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td auto-width>
              <q-btn flat dense round icon="delete" @click="handleDeleteStudent(props.row)" />
            </q-td>
            <q-td v-for="col in props.cols" :key="col.name" :props="props">
              {{ col.value }}
            </q-td>
          </q-tr>
        </template>
      </q-table>
      <q-btn
        no-caps
        label="Next Step"
        color="black"
        text-color="white"
        @click="
          () => {
            isDisplayStep3 = false;
            isDisplayStep4 = false;
            isDisplayLastStep = true;
          }
        "
      />
    </div>
    <div class="q-pa-md" v-if="isDisplayLastStep">
      <div class="text-h6">Last step: You are almost there!</div>
      <div>
        Click the button below to go to the course page. Then you can add modules and chatbots to
        the course.
      </div>
      <q-btn
        no-caps
        label="Go to the Course Page"
        color="black"
        text-color="white"
        @click="handleRediect()"
      />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';

const $q = useQuasar();
const router = useRouter();
// const route = useRoute();

const isFetching = ref(true);

const urlToNewCourse = (courseId: string) => `/teacher/course/${courseId}`;
const email = ref<string>('');
const newStudentGroupName = ref<string>('');
const newStudentGroupDesc = ref<string>('');
const newStudentGroupId = ref<string>('');
const existingStudentGroupId = ref<string>('');

const newCourseId = ref<string>('');
const newCourseTitle = ref<string>('');
const newCourseStartDate = ref<string>(new Date().toISOString().substr(0, 10));
const newCourseEndDate = ref<string>(
  new Date(new Date().setMonth(new Date().getMonth() + 5)).toISOString().substr(0, 10),
);

const isDisplayStep1 = ref<boolean>(true);
const isDisplayStep2 = ref<boolean>(false);
const isDisplayStep3 = ref<boolean>(false);
const isDisplayStep4 = ref<boolean>(false);
const isDisplayLastStep = ref<boolean>(false);

const handleCreateStudentGroup = async () => {
  // Handle creating a new student group
  try {
    // Trim the student group name
    newStudentGroupName.value = newStudentGroupName.value.trim();
    // Validate the student group name
    if (!newStudentGroupName.value) {
      $q.notify({
        type: 'negative',
        message: 'Student Group Name is required',
      });
      return;
    }
    // Trim the student group description
    newStudentGroupDesc.value = newStudentGroupDesc.value.trim();
    // Not compulsory
    // if (!newStudentGroupDesc.value) {
    //   $q.notify({
    //     type: 'negative',
    //     message: 'Student Group Description is required'
    //   });
    //   return;
    // }
    // Create a new student group
    const response = await api.post('/group', {
      group_name: newStudentGroupName.value,
      description_content: newStudentGroupDesc.value,
    });
    // console.log(response.data);
    $q.dialog({
      title: 'Student Group Created',
      message:
        'Student Group created successfully. The Student Group ID is ' +
        response.data.group_id +
        ', please keep it safe for future reference.',
      ok: 'OK',
    }).onOk(() => {
      isDisplayStep1.value = false;
      isDisplayStep2.value = true;
    });
    // Update the student group id
    newStudentGroupId.value = response.data.group_id;
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to create student group: ' + String(error),
    });
  }
};

const handleConnectStudentGroup = async () => {
  // Handle associating with an existing student group
  try {
    // Trim the student group id
    existingStudentGroupId.value = existingStudentGroupId.value.trim();
    // Validate the student group id
    if (!existingStudentGroupId.value) {
      $q.notify({
        type: 'negative',
        message: 'Existing Student Group ID is required',
      });
      return;
    }
    // Check if the student group id exists
    const response = await api.get('/group', {
      params: {
        group_id: existingStudentGroupId.value,
      },
    });
    // console.log(response.data);
    // If the student group id exists, update the student group id
    if (response.data) {
      newStudentGroupId.value = existingStudentGroupId.value;
      $q.notify({
        type: 'positive',
        message: 'Student Group ' + existingStudentGroupId.value + ' connected successfully',
      });
      isDisplayStep1.value = false;
      isDisplayStep2.value = true;
      return;
    } else {
      $q.notify({
        type: 'negative',
        message: 'Student Group ' + existingStudentGroupId.value + ' does not exist',
      });
    }
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to connect student group: ' + String(error),
    });
  }
};

const handleCreateCourse = async () => {
  // Handle creating a new course
  try {
    // Trim the course title
    newCourseTitle.value = newCourseTitle.value.trim();
    // Validate the course title
    if (!newCourseTitle.value) {
      $q.notify({
        type: 'negative',
        message: 'Course Title is required',
      });
      return;
    }
    // Trim the course start date
    newCourseStartDate.value = newCourseStartDate.value.trim();
    // Validate the course start date
    if (!newCourseStartDate.value) {
      $q.notify({
        type: 'negative',
        message: 'Course Start Date is required',
      });
      return;
    }
    // Trim the course end date
    newCourseEndDate.value = newCourseEndDate.value.trim();
    // Validate the course end date
    if (!newCourseEndDate.value) {
      $q.notify({
        type: 'negative',
        message: 'Course End Date is required',
      });
      return;
    }
    // Create a new course
    const response = await api.post('/course', {
      course_title: newCourseTitle.value,
      group_id: newStudentGroupId.value,
      start_date: new Date(newCourseStartDate.value)
        .toLocaleString('en-US', { timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone })
        .toString(),
      end_date: new Date(newCourseEndDate.value)
        .toLocaleString('en-US', { timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone })
        .toString(),
    });
    // console.log(response.data);
    $q.notify({
      type: 'positive',
      message: 'Course created successfully',
    });
    isDisplayStep2.value = false;
    isDisplayStep3.value = true;
    isDisplayStep4.value = true;

    // Update the rows
    await fetchCourseStudentList(response.data.course_id);
    // Update the course id
    newCourseId.value = response.data.course_id;
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to create course: ' + String(error),
    });
  }
};

interface Row {
  personnel_id: string;
  email: string;
  full_name: string;
}

interface Column {
  name: string;
  label: string;
  align: 'left' | 'right' | 'center';
  field: string | ((row: Row) => number | string);
  sortable?: boolean;
  required?: boolean;
}

const columns: Column[] = [
  {
    name: 'Index',
    required: true,
    label: 'Index',
    align: 'left',
    field: (row: Row) => rows.value.indexOf(row) + 1,
    sortable: true,
  },
  {
    name: 'personnel_id',
    label: 'Student ID',
    align: 'left',
    field: 'personnel_id',
    sortable: true,
  },
  {
    name: 'email',
    label: 'Email',
    align: 'left',
    field: 'email',
    sortable: true,
  },
  {
    name: 'full_name',
    label: 'Full Name',
    align: 'left',
    field: 'full_name',
    sortable: true,
  },
];

const rows = ref<Row[]>([]);

const courseStudentList = ref([
  {
    group_course_id: '1',
    group_id: '1',
    coruse_id: '1',
    group_name: 'Group 1',
    student_id: '1',
    username: 'johndoe',
    full_name: 'John Doe',
    email: '<EMAIL>',
    personnel_id: '1',
  },
]);

const handleAddStudent = async () => {
  // Handle adding student to the course
  try {
    // Trim the email
    email.value = email.value.trim();
    // Validate the email
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(email.value)) {
      $q.notify({
        type: 'negative',
        message: 'Email format is invalid',
      });
      return;
    }
    // Add student to the course
    await api.post('/email-enrollment', {
      group_id: newStudentGroupId.value,
      email: email.value,
    });
    // console.log(response.data);
    $q.notify({
      type: 'positive',
      message: 'Student added successfully',
    });
    // Update the rows
    await fetchCourseStudentList(newCourseId.value);
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to add student: ' + String(error),
    });
  }
};

const handleDeleteStudent = (row: Row) => {
  // Handle deleting student from the course
  // Get the user_id from courseStudentList by row
  const studentId = courseStudentList.value.find(
    (student) => student.email === row.email,
  )?.student_id;
  try {
    $q.dialog({
      title: 'Delete Student',
      message: 'Are you sure you want to delete the student (' + row.full_name + ')?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Delete student from the course
        await api.delete('/enrollment', {
          params: {
            group_id: newStudentGroupId.value,
            student_id: studentId,
          },
        });
        // console.log(response.data);
        $q.notify({
          type: 'positive',
          message: 'Student deleted successfully',
        });
        // Update the rows
        await fetchCourseStudentList(newCourseId.value);
      })();
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to delete student: ' + String(error),
    });
  }
};

const fetchCourseStudentList = async (courseId: string) => {
  // Fetch course student list from the server
  const response = await api.get('/course-student-list', {
    params: {
      course_id: courseId,
    },
  });
  // console.log(response.data);
  // Update the rows with the fetched data
  rows.value = response.data;
  courseStudentList.value = response.data;
};

const handleRediect = async () => {
  try {
    // Wait for the route change to complete
    await router.push(urlToNewCourse(newCourseId.value));
    // Force refresh the page
    window.location.reload();
  } catch (error) {
    console.error('Failed to redirect:', error);
  }
};

onMounted(() => {
  isFetching.value = true;
  // await fetchCourseStudentList(newCourseId.value);
  isFetching.value = false;
});
</script>

<style scoped></style>
