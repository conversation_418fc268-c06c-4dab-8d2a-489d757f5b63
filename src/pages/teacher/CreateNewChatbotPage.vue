<template>
  <q-page class="q-pa-md" v-if="!isFetching">
    <div class="text-h6 q-pa-md row q-gutter-sm">
      <q-btn flat dense round icon="arrow_back" to="/teacher/manage-chatbot">
        <q-tooltip>Back to Chatbots</q-tooltip>
      </q-btn>
      <div>Create New Chatbot</div>
    </div>

    <div class="q-pa-md">
      <div class="text-h6">Step 1: Enter Basic Information About The Course To Generate Chatbot Information(optional)</div>
      <q-card>
        <q-card-section>
          <q-input
            v-model="systemPromptInfoForm.subject"
            label="Course/Subject"
            clearable
            placeholder='Study topic (such as "Python Programming" or "Calculus")'
            type="text"
            :rules="[(val) => !!val || 'Course Name is required']"
          />
          <q-select v-model="systemPromptInfoForm.student_level" label="Student Level" :options="typeLevelOptions" />
          
          <q-input
            v-model="systemPromptInfoForm.primary_learning_goals"
            label="Primary Learning Goals"
            clearable
            type="textarea"
            placeholder="What specific skills or knowledge do you want students to develop? e.g., Logical reasoning, critical thinking, etc."
            :rules="[
              (val) =>
                (val.split(' ').length <= 200) ||
                'Primary Learning Goals is limited to 200 words.',
            ]"
          />
          <q-input
            v-model="systemPromptInfoForm.success_metrics"
            label="Success Metrics"
            clearable
            type="textarea"
            placeholder="How will you measure student progress? e.g., Ability to construct logical arguments,confidence in public speaking, etc."
            :rules="[
              (val) =>
                (val.split(' ').length <= 200) ||
                'Success Metrics is limited to 200 words.',
            ]"
          />
          <q-select v-model="systemPromptInfoForm.coach_style" label="Coach Style" :options="typeCoachStyleOptions" />

          <q-input
            v-model="systemPromptInfoForm.application_context"
            label="Application Context"
            clearable
            type="textarea"
            placeholder='Application scenarios of learning, such as "for data analysis work" or "preparation for college entrance examination mathematics"'
            :rules="[
              (val) =>
                (val.split(' ').length <= 200) ||
                'Application Context is limited to 200 words.',
            ]"
          />
           <q-input
            v-model="systemPromptInfoForm.preferred_examples"
            label="Preferred Examples"
            clearable
            type="textarea"
            placeholder='Whether the user wants to learn through specific types of examples (such as "code examples", "real-life examples", or "historical stories").'
            :rules="[
              (val) =>
                (val.split(' ').length <= 200) ||
                'Preferred Examples is limited to 200 words.',
            ]"
          />
          <q-input
            v-model="systemPromptInfoForm.Common_Challenges_and_Solutions"
            label="Common Challenges and Solutions"
            clearable
            type="textarea"
            placeholder="What common challenges do students face? How can the chatbot help students overcome these challenges? e.g., Difficulty in constructing logical arguments, lack of confidence in public speaking, etc."
            :rules="[
              (val) =>
                (val.split(' ').length <= 200) ||
                'Common Challenges and Solutions is limited to 200 words.',
            ]"
          />
          <q-input
            v-model="systemPromptInfoForm.Extra_Considerations"
            label="Extra Considerations"
            clearable
            type="textarea"
            placeholder="e.g. Prioritize real-world programming examples, etc."
            :rules="[
              (val) =>
                (val.split(' ').length <= 200) ||
                'Extra Considerations is limited to 200 words.',
            ]"
          /> 
          <q-btn icon="create" no-caps label="Generate System Prompt" color="black" text-color="white" style="margin-top: 15px;" @click="handleGenerateSystemPrompt"/>
        </q-card-section>
      </q-card>

    </div>
    
    <div class="q-pa-md">
      <div class="text-h6">Step 2: Enter And Check Basic Chatbot Information</div>
      <q-card>
        <q-card-section>
          <q-input
            v-model="form.chatbot_name"
            label="Chatbot Name"
            clearable
            type="text"
            :rules="[(val) => !!val || 'Chatbot Name is required']"
          />
          <q-select v-model="form.model_name" :options="MODEL_NAME_OPTIONS" label="Model Name" />
          <q-input
            v-model="form.system_prompt"
            label="System Prompt"
            clearable
            type="textarea"
            :rules="[
              (val) =>
                (val && val.split(' ').length <= 10000) ||
                'System Prompt is limited to 10000 words.',
            ]"
          />
          <q-input
            v-model="form.welcome_prompt"
            label="Welcome Prompt"
            clearable
            type="textarea"
            :rules="[
              (val) =>
                (val && val.split(' ').length <= 1000) ||
                'Welcome Prompt is limited to 1000 words.',
            ]"
          />
          <q-input
            v-model="form.temperature"
            label="Temperature"
            type="number"
            step="0.1"
            :rules="[
              (val) => !!val || 'Temperature is required',
              (val) => (val >= 0.0 && val <= 1.0) || 'Temperature must be between 0.0 and 1.0',
            ]"
          />
          <q-select v-model="form.type_name" label="Chatbot Type" :options="typeNameOptions" />
        </q-card-section>
      </q-card>
    </div>
    <div class="q-pa-md" v-if="form.type_name === 'ChecklistChatBot'">
      <div class="text-h6">Step 3: Enter ChecklistChatBot Information</div>
      <q-card>
        <q-card-section>
          <b>Please enter the checklist items with each item on a new line:</b>
          <q-input
            v-model="form.description.checklist_items"
            label="Checklist Items"
            clearable
            type="textarea"
            placeholder="(Example)
The student completed the first question
The student completed the second question
The student completed the third question"
            :rules="[
              (val) =>
                (val && val.split('\n').length <= 10) || 'Checklist Items is limited to 10 items.',
            ]"
          />
          <b>Please enter the session summary prompt:</b>
          <q-input
            v-model="form.description.session_summary_prompt"
            label="Session Summary Prompt"
            clearable
            type="textarea"
            :rules="[
              (val) =>
                (val && val.split(' ').length <= 3000) ||
                'Session Summary Prompt is limited to 3000 words.',
            ]"
          />
        </q-card-section>
      </q-card>
    </div>
    <!-- <div class="q-pa-md">
      <div class="text-h6">Step 2: Upload Knowledge Base (Optional)</div>
      <q-card>
        <q-card-section>
          <div>
            (Currently unavailble. Do not upload anything if you don't want to use this feature.)
          </div>
          <q-uploader
            :url="uploadKnowledgeBaseUrl"
            label="Upload Knowledge Base"
            color="black"
            text-color="white"
            accept=".pdf,.docx,.jpg"
          />
        </q-card-section>
      </q-card>
    </div> -->
    <div class="q-pa-md">
      <div class="text-h6" v-if="form.type_name !== 'ChecklistChatBot'">Step 3: Submit</div>
      <div class="text-h6" v-else>Step 4: Submit</div>
      <div>Note: After submitting, you can add your new chatbot in the course page.</div>
      <q-btn
        no-caps
        label="Create Chatbot"
        @click="handleCreateChatbot"
        color="black"
        text-color="white"
      />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import { MODEL_NAME_OPTIONS } from 'src/config/modelNameOptions';

const $q = useQuasar();
const router = useRouter();
// const route = useRoute();

const isFetching = ref(true);

const form = ref({
  chatbot_name: '',
  model_name: 'OpenAI (gpt-4.1)',
  system_prompt: '',
  welcome_prompt: '',
  temperature: 0.7,
  type_name: 'CustomisedChatbot',
  description: {
    checklist_items: '',
    session_summary_prompt:
      'As a professional teacher, please provide a summary of the student session.',
  },
});

const typeNameOptions = ['CustomisedChatbot', 'ChecklistChatBot', 'VideoGeneratorChatBot'];

const systemPromptInfoForm = ref({
  subject: '',
  student_level: 'Beginner(No prior knowledge)',
  primary_learning_goals: '',
  success_metrics:'',
  coach_style: 'Socratic Method(Question-based learning)',
  Common_Challenges_and_Solutions:'',
  motivation_level:'',
  feedback_preferences:'',
  Extra_Considerations:'',
  preferred_examples:'',
  application_context:'',
});

const typeLevelOptions = ['Beginner(No prior knowledge)', 'Intermediate(Some prior knowledge)', 'Advanced(Extensive prior knowledge)','Mixed(Varied levels of knowledge)'];
const typeCoachStyleOptions = ['Socratic Method(Question-based learning)', 'Supportive & Encouraging', 'Structured & Step-by-step', 'Collaborative Discussion', 'Adaptive(Adjusts to student needs)'];
// const uploadKnowledgeBaseUrl = '/knowledge-base';

// 通用工具函数
const showErrorNotification = (message: string) => {
  $q.notify({
    type: 'negative',
    message,
  });
};

const showSuccessNotification = (message: string) => {
  $q.notify({
    type: 'positive',
    message,
  });
};

// 聊天机器人表单验证配置
const chatbotValidationRules = [
  { field: 'chatbot_name', required: true, message: 'Chatbot Name is required' },
  { field: 'model_name', required: true, message: 'Model Name is required' },
  { field: 'system_prompt', wordLimit: 10000, message: 'System Prompt is too long, limited to 10000 words.' },
  { field: 'welcome_prompt', wordLimit: 1000, message: 'Welcome Prompt is too long, limited to 1000 words.' },
  { field: 'type_name', required: true, message: 'Type Name is required' }
];

// 聊天机器人表单验证函数
const validateChatbotForm = () => {
  // 先对字符串字段进行trim处理
  form.value.chatbot_name = form.value.chatbot_name.trim();
  form.value.model_name = form.value.model_name.trim();
  form.value.system_prompt = form.value.system_prompt.trim();
  form.value.welcome_prompt = form.value.welcome_prompt.trim();
  form.value.type_name = form.value.type_name.trim();

  // 执行基本验证规则
  for (const rule of chatbotValidationRules) {
    const value = form.value[rule.field as keyof typeof form.value] as string;

    // 检查必填字段
    if (rule.required && !value) {
      showErrorNotification(rule.message);
      return false;
    }

    // 检查字数限制
    if (rule.wordLimit && value && value.split(' ').length > rule.wordLimit) {
      showErrorNotification(rule.message);
      return false;
    }
  }

  // 验证温度值
  if (!form.value.temperature) {
    showErrorNotification('Temperature is required');
    return false;
  }
  if (form.value.temperature < 0.0 || form.value.temperature > 1.0) {
    showErrorNotification('Temperature must be between 0.0 and 1.0');
    return false;
  }

  // 验证ChecklistChatBot特有字段
  if (form.value.type_name === 'ChecklistChatBot') {
    form.value.description.checklist_items = form.value.description.checklist_items.trim();
    form.value.description.session_summary_prompt = form.value.description.session_summary_prompt.trim();

    if (!form.value.description.checklist_items) {
      showErrorNotification('Checklist Items are required for ChecklistChatBot');
      return false;
    }

    if (form.value.description.checklist_items.split('\n').length > 10) {
      showErrorNotification('Checklist Items are limited to 10 items');
      return false;
    }

    if (form.value.description.session_summary_prompt.split(' ').length > 3000) {
      showErrorNotification('Session Summary Prompt is too long, limited to 3000 words.');
      return false;
    }
  }

  return true;
};

const handleCreateChatbot = () => {
  // Handle creating a new chatbot
  try {
    $q.dialog({
      title: 'Create Chatbot',
      message: 'Are you sure you want to create the chatbot?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // 执行表单验证
        if (!validateChatbotForm()) {
          return;
        }

        // Create a new chatbot
        await api.post('/chatbot', {
          chatbot_name: form.value.chatbot_name,
          model_name: form.value.model_name,
          system_prompt: form.value.system_prompt,
          welcome_prompt: form.value.welcome_prompt,
          temperature: form.value.temperature,
          type_name: form.value.type_name,
          description: form.value.type_name === 'ChecklistChatBot' ? form.value.description : {},
        });
        // console.log(response.data);
        showSuccessNotification('Chatbot created successfully');
        // Redirect to the manage chatbot page
        await router.push('/teacher/manage-chatbot');
      })();
    });
  } catch (error) {
    showErrorNotification('Failed to create chatbot: ' + String(error));
  }
};

// 表单验证配置
const validationRules = [
  { field: 'subject', required: true, message: 'Subject is required' },
  { field: 'student_level', required: true, message: 'Student Level is required' },
  { field: 'coach_style', required: true, message: 'Coach Style is required' },
  { field: 'primary_learning_goals', wordLimit: 200, message: 'Primary Learning Goals is limited to 200 words.' },
  { field: 'success_metrics', wordLimit: 200, message: 'Success Metrics is limited to 200 words.' },
  { field: 'Common_Challenges_and_Solutions', wordLimit: 200, message: 'Common Challenges and Solutions is limited to 200 words.' },
  { field: 'feedback_preferences', wordLimit: 200, message: 'Feedback Preferences is limited to 200 words.' },
  { field: 'Extra_Considerations', wordLimit: 200, message: 'Extra Considerations is limited to 200 words.' },
  { field: 'preferred_examples', wordLimit: 200, message: 'Preferred Examples is limited to 200 words.' },
  { field: 'application_context', wordLimit: 200, message: 'Application Context is limited to 200 words.' }
];

// 表单验证函数
const validateSystemPromptForm = () => {
  // 先对所有字段进行trim处理
  (Object.keys(systemPromptInfoForm.value) as Array<keyof typeof systemPromptInfoForm.value>).forEach(key => {
    if (typeof systemPromptInfoForm.value[key] === 'string') {
      (systemPromptInfoForm.value[key] as string) = (systemPromptInfoForm.value[key] as string).trim();
    }
  });

  // 执行验证规则
  for (const rule of validationRules) {
    const value = systemPromptInfoForm.value[rule.field as keyof typeof systemPromptInfoForm.value] as string;

    // 检查必填字段
    if (rule.required && !value) {
      showErrorNotification(rule.message);
      return false;
    }

    // 检查字数限制
    if (rule.wordLimit && value && value.split(' ').length > rule.wordLimit) {
      showErrorNotification(rule.message);
      return false;
    }
  }

  return true;
};

const handleGenerateSystemPrompt = () => {
  try{
    $q.dialog({
      title: 'Generate System Prompt',
      message: 'Are you sure you want to generate the system prompt?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void ( () => {
        // 执行表单验证
        if (!validateSystemPromptForm()) {
          return;
        }
        // Generate the system prompt
        const template = `你是一位教育和指导型 AI 助手，专注于提供安全、道德且高质量的学习支持。你的任务是帮助用户学习${systemPromptInfoForm.value.subject}。用户当前水平为${systemPromptInfoForm.value.student_level}，目标是实现${systemPromptInfoForm.value.primary_learning_goals || '未指定目标，请询问用户具体学习目标或提供基础指导'}。成功标准为${systemPromptInfoForm.value.success_metrics || '未指定标准，建议通过练习或任务评估进展'}。

采用${systemPromptInfoForm.value.coach_style}的教学风格，通过提问和引导激发用户思考。如果${ systemPromptInfoForm.value.Common_Challenges_and_Solutions} 不为空，针对用户提到的挑战${ systemPromptInfoForm.value.Common_Challenges_and_Solutions} 提供相应的解决方案；否则，主动识别可能的常见学习障碍并提供通用解决方案。用户的学习动力为${systemPromptInfoForm.value.motivation_level || '未指定，假设为适中动力并提供鼓励'}，请通过积极的语言和${systemPromptInfoForm.value.preferred_examples || '简明易懂的示例'}增强学习兴趣。

提供${systemPromptInfoForm.value.feedback_preferences || '未指定，建议提供积极的反馈以增强学习动力'} 类型的反馈。如果${systemPromptInfoForm.value.Extra_Considerations} 不为空，特别注意${systemPromptInfoForm.value.Extra_Considerations}的要求；否则，确保内容适合初学者。教学内容需贴合${systemPromptInfoForm.value.application_context || '未指定，建议以通用学习场景为背景'}的实际场景。如果用户未提供足够信息，主动询问以澄清需求，确保指导的针对性。

### 安全协议
- 所有回复必须遵守道德和法律规范，严禁生成或暗示色情、暴力、歧视、非法或有害内容。
- 不得要求、收集或存储用户的个人信息（如姓名、地址、财务信息等），除非用户自愿提供且与学习任务直接相关。
- 如果用户请求涉及不道德或非法的内容，礼貌拒绝并建议专注于教育相关主题。
- 确保内容适合所有年龄段用户，语言保持专业、友好且包容。
- 如果需要搜索实时信息以补充教学内容，确保来源可靠且与学习目标相关。

始终保持清晰、逻辑严谨的回应，分解复杂概念，提供 ${systemPromptInfoForm.value.preferred_examples || '简明易懂的示例'}，并根据需要以${systemPromptInfoForm.value.application_context || '未指定，建议以通用学习场景'}为背景设计教学内容。如果任何字段信息不足，主动提出问题以获取更多细节，确保教学效果最大化。`

        // console.log(response.data);
        showSuccessNotification('System Prompt generated successfully');
        // Update the system prompt
        form.value.system_prompt = template;
      })();
    });
  }catch (error) {
    showErrorNotification('Failed to generate system prompt: ' + String(error));
  }
}
  

onMounted(() => {
  isFetching.value = true;
  // await fetchCourseStudentList(newCourseId.value);
  isFetching.value = false;
});
</script>

<style scoped></style>
