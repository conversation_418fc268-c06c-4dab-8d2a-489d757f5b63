<template>
  <q-page class="q-pa-md" v-if="!isFetching">
    <div class="text-h6 q-pa-md row q-gutter-sm">
      <q-btn flat dense round icon="arrow_back" to="/teacher/homepage">
        <q-tooltip>Back to Homepage</q-tooltip>
      </q-btn>
      <div>Create New Notification</div>
    </div>
    <div class="q-pa-md">
      <div class="text-h6">Step 1: Enter Notification Information</div>
      <q-card>
        <q-card-section>
          <q-select v-model="form.course" :options="courseOptions" label="Select Course" />
          <q-input
            v-model="form.notification_title"
            label="Notification Title"
            clearable
            type="text"
          />
          <q-input
            v-model="form.notification_content"
            label="Notification Content"
            clearable
            type="textarea"
          />
        </q-card-section>
      </q-card>
    </div>
    <div class="q-pa-md">
      <div class="text-h6">Step 2: Submit</div>
      <div>Note: After submitting, you can view your new notification in the Homepage.</div>
      <q-btn
        no-caps
        label="Create Notification"
        @click="handleCreateNotification"
        color="black"
        text-color="white"
      />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';

const $q = useQuasar();
const router = useRouter();
// const route = useRoute();

const isFetching = ref(true);

const form = ref({
  course: { label: '', value: '' },
  notification_title: '',
  notification_content: '',
});

const courseOptions = ref([
  { label: 'Course 1', value: '1' },
  { label: 'Course 2', value: '2' },
  { label: 'Course 3', value: '3' },
  { label: 'Course 4', value: '4' },
  { label: 'Course 5', value: '5' },
]);

interface CourseResponseItem {
  course_id: string;
  teacher_id: string;
  courses: Course;
}

interface Course {
  id: number;
  course_id: string;
  created_at: string;
  deleted_at: string;
  updated_at: string;
  description: JSON;
  course_title: string;
  creator_user_id: string;
}

const courseDataToOption = (data: CourseResponseItem[]) => {
  return data
    .map((item) => {
      // If the creator_user_id is not the teacher_id, skip this item
      if (item.courses.creator_user_id !== item.teacher_id) {
        return null; // Jump the current item
      }
      return {
        label: item.courses.course_title,
        value: item.courses.course_id,
      } as { label: string; value: string };
    })
    .filter((item) => item !== null) as { label: string; value: string }[];
};

const handleCreateNotification = () => {
  // Handle creating a new notification
  try {
    $q.dialog({
      title: 'Create Notification',
      message: 'Are you sure you want to create the notification?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Trim the course_id
        form.value.course.value = form.value.course.value.trim();
        // Verify the course_id
        if (form.value.course.value === '') {
          $q.notify({
            type: 'negative',
            message: 'Course is required',
          });
          return;
        }
        // Trim the notification_title
        form.value.notification_title = form.value.notification_title.trim();
        // Verify the notification_title
        if (form.value.notification_title === '') {
          $q.notify({
            type: 'negative',
            message: 'Notification Title is required',
          });
          return;
        }
        // Trim the notification_content
        form.value.notification_content = form.value.notification_content.trim();
        // Verify the notification_content
        if (form.value.notification_content === '') {
          $q.notify({
            type: 'negative',
            message: 'Notification Content is required',
          });
          return;
        }
        // Create a new notification
        await api.post('/notification', {
          course_id: form.value.course.value,
          notification_title: form.value.notification_title,
          notification_content: form.value.notification_content,
        });
        // console.log(response.data);
        $q.notify({
          type: 'positive',
          message: 'Notification created successfully',
        });
        // Redirect to the Homepage
        await router.push('/teacher/homepage');
      })();
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to create notification: ' + String(error),
    });
  }
};

const fetchCourseList = async () => {
  // Fetch course list from the server
  const response = await api.get('/course-list');
  console.log(response.data);
  // Update the courseList with the fetched data
  courseOptions.value = courseDataToOption(response.data);

  // If there are no courses, notify the user
  if (courseOptions.value.length === 0) {
    $q.notify({
      type: 'negative',
      message: 'No courses found, please create a course first.',
    });
    // Redirect to the Homepage
    await router.push('/teacher/homepage');
  }
};

onMounted(async () => {
  isFetching.value = true;
  await fetchCourseList();
  isFetching.value = false;
});
</script>

<style scoped></style>
