<template>
  <q-page class="q-pa-md" v-if="!isFetching">
    <div class="text-h6 q-pa-md row q-gutter-sm">
      <q-btn flat dense round icon="arrow_back" @click="handleBackNavigation">
        <q-tooltip>Back</q-tooltip>
      </q-btn>
      <div>Setting Chatbot - {{ form.chatbot_name }}</div>
    </div>
    <div class="q-pa-md">
      <div class="text-h6">Step 1: Enter Basic Chatbot Information</div>
      <q-card>
        <q-card-section>
          <q-input
            v-model="form.chatbot_name"
            label="Chatbot Name"
            clearable
            type="text"
            :rules="[(val) => !!val || 'Chatbot Name is required']"
          />
          <q-select v-model="form.model_name" :options="MODEL_NAME_OPTIONS" label="Model Name" />
          <q-input
            v-model="form.system_prompt"
            label="System Prompt"
            clearable
            type="textarea"
            :rules="[
              (val) =>
                (val && val.split(' ').length <= 10000) ||
                'System Prompt is limited to 10000 words.',
            ]"
          />
          <q-input
            v-model="form.welcome_prompt"
            label="Welcome Prompt"
            clearable
            type="textarea"
            :rules="[
              (val) =>
                (val && val.split(' ').length <= 1000) ||
                'Welcome Prompt is limited to 1000 words.',
            ]"
          />
          <q-input
            v-model="form.temperature"
            label="Temperature"
            type="number"
            step="0.1"
            :rules="[
              (val) => !!val || 'Temperature is required',
              (val) => (val >= 0.0 && val <= 1.0) || 'Temperature must be between 0.0 and 1.0',
            ]"
          />
          <q-select
            v-model="form.type_name"
            label="Chatbot Type"
            :options="typeNameOptions"
            @update:model-value="
              (val) => {
                form.type_name = val;
                if (val === 'ChecklistChatBot') {
                  form.description = {
                    checklist_items: '',
                    session_summary_prompt:
                      'As a professional teacher, please provide a summary of the student session.',
                  };
                } else {
                  form.description = {
                    checklist_items: '',
                    session_summary_prompt: '',
                  };
                }
              }
            "
          />
        </q-card-section>
      </q-card>
    </div>
    <div class="q-pa-md" v-if="form.type_name === 'ChecklistChatBot'">
      <div class="text-h6">Step 2: Enter ChecklistChatBot Information</div>
      <q-card>
        <q-card-section>
          <b>Please enter the checklist items with each item on a new line:</b>
          <q-input
            v-model="form.description.checklist_items"
            label="Checklist Items"
            clearable
            type="textarea"
            placeholder="(Example)
The student completed the first question
The student completed the second question
The student completed the third question"
            :rules="[
              (val) =>
                (val && val.split('\n').length <= 10) || 'Checklist Items is limited to 10 items.',
            ]"
          />
          <b>Please enter the session summary prompt:</b>
          <q-input
            v-model="form.description.session_summary_prompt"
            label="Session Summary Prompt"
            clearable
            type="textarea"
            :rules="[
              (val) =>
                (val && val.split(' ').length <= 3000) ||
                'Session Summary Prompt is limited to 3000 words.',
            ]"
          />
        </q-card-section>
      </q-card>
    </div>
    <!-- <div class="q-pa-md" v-if="form.type_name !== 'ChecklistChatBot'">
      <div class="text-h6">Step 2: Upload Knowledge Base (Optional)</div>
      <q-card>
        <q-card-section>
          <div>
            (Currently unavailble. Do not upload anything if you don't want to use this feature.)
          </div>
          <q-uploader
            :url="uploadKnowledgeBaseUrl"
            label="Upload Knowledge Base"
            color="black"
            text-color="white"
            accept=".pdf,.docx,.jpg"
          />
        </q-card-section>
      </q-card>
    </div> -->
    <div class="q-pa-md">
      <div class="text-h6" v-if="form.type_name === 'ChecklistChatBot'">Step 3: Submit</div>
      <div class="text-h6" v-else>Step 2: Submit</div>
      <q-btn
        no-caps
        label="Update Chatbot"
        @click="handleUpdateChatbot(selectedChatbotId)"
        color="black"
        text-color="white"
      />
    </div>
    <q-separator spaced />
    <div class="q-pa-md">
      <div class="text-h6">Danger Zone</div>
      <q-btn
        no-caps
        label="Delete Chatbot"
        @click="handleDeleteChatbot(selectedChatbotId)"
        color="red"
        text-color="white"
      />
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import { MODEL_NAME_OPTIONS } from 'src/config/modelNameOptions';

const $q = useQuasar();
const router = useRouter();
const route = useRoute();

const isFetching = ref(true);

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

const selectedChatbotId = ref<string>('');
const sourceRoute = ref<string>('');
const courseId = ref<string>('');

const form = ref({
  chatbot_name: '',
  model_name: 'OpenAI (gpt-4.1)',
  system_prompt: '',
  welcome_prompt: '',
  temperature: 0.7,
  type_name: 'CustomisedChatbot',
  description: {
    checklist_items: '',
    session_summary_prompt: '',
  },
});

const typeNameOptions = ['CustomisedChatbot', 'ChecklistChatBot', 'VideoGeneratorChatBot'];

// const uploadKnowledgeBaseUrl = '/knowledge-base';

const handleBackNavigation = () => {
  if (sourceRoute.value === 'course' && courseId.value) {
    void router.push(`/teacher/course/${courseId.value}`);
  } else {
    void router.push('/teacher/manage-chatbot');
  }
};

const handleUpdateChatbot = (selectedChatbotIdValue: string) => {
  // Handle setup a new chatbot
  try {
    $q.dialog({
      title: 'Update Chatbot',
      message: 'Are you sure you want to update the chatbot?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Trim the chatbot name
        form.value.chatbot_name = form.value.chatbot_name.trim();
        // Validate the chatbot name
        if (!form.value.chatbot_name) {
          $q.notify({
            type: 'negative',
            message: 'Chatbot Name is required',
          });
          return;
        }
        // Trim the model name
        form.value.model_name = form.value.model_name.trim();
        // Validate the model name
        if (!form.value.model_name) {
          $q.notify({
            type: 'negative',
            message: 'Model Name is required',
          });
          return;
        }
        // Trim the system prompt
        form.value.system_prompt = form.value.system_prompt.trim();
        // Validate the system prompt, limited to 10000 words
        if (form.value.system_prompt.split(' ').length > 10000) {
          $q.notify({
            type: 'negative',
            message: 'System Prompt is too long, limited to 10000 words.',
          });
          return;
        }
        // Trim the welcome prompt
        form.value.welcome_prompt = form.value.welcome_prompt.trim();
        // Validate the welcome prompt, limited to 1000 words
        if (form.value.welcome_prompt.split(' ').length > 1000) {
          $q.notify({
            type: 'negative',
            message: 'Welcome Prompt is too long, limited to 1000 words.',
          });
          return;
        }
        // Validate the Temperature
        if (!form.value.temperature) {
          $q.notify({
            type: 'negative',
            message: 'Temperature is required',
          });
          return;
        }
        if (form.value.temperature < 0.0 || form.value.temperature > 1.0) {
          $q.notify({
            type: 'negative',
            message: 'Temperature must be between 0.0 and 1.0',
          });
          return;
        }
        // Trim the type name
        form.value.type_name = form.value.type_name.trim();
        // Validate the type name
        if (!form.value.type_name) {
          $q.notify({
            type: 'negative',
            message: 'Type Name is required',
          });
          return;
        }
        // Validate checklist items if type is ChecklistChatBot
        if (form.value.type_name === 'ChecklistChatBot') {
          // Trim checklist items
          form.value.description.checklist_items = form.value.description.checklist_items.trim();
          // Validate checklist items
          if (!form.value.description.checklist_items) {
            $q.notify({
              type: 'negative',
              message: 'Checklist Items are required for ChecklistChatBot',
            });
            return;
          }
          if (form.value.description.checklist_items.split('\n').length > 10) {
            $q.notify({
              type: 'negative',
              message: 'Checklist Items are limited to 10 items',
            });
            return;
          }
          // Trim session summary prompt
          form.value.description.session_summary_prompt =
            form.value.description.session_summary_prompt.trim();
          // Validate session summary prompt, limited to 3000 words
          if (form.value.description.session_summary_prompt.split(' ').length > 3000) {
            $q.notify({
              type: 'negative',
              message: 'Session Summary Prompt is too long, limited to 3000 words.',
            });
            return;
          }
        }
        // Setup the chatbot
        await api.put('/chatbot', {
          chatbot_id: selectedChatbotIdValue,
          chatbot_name: form.value.chatbot_name,
          model_name: form.value.model_name,
          system_prompt: form.value.system_prompt,
          welcome_prompt: form.value.welcome_prompt,
          temperature: form.value.temperature,
          type_name: form.value.type_name,
          description: form.value.type_name === 'ChecklistChatBot' ? form.value.description : {},
        });
        // console.log(response.data);
        $q.notify({
          type: 'positive',
          message: 'Chatbot updated successfully',
        });
        // Navigate back based on source route
        handleBackNavigation();
      })();
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to setup chatbot: ' + String(error),
    });
  }
};

const handleDeleteChatbot = (selectedChatbotIdValue: string) => {
  // Handle delete chatbot
  try {
    $q.dialog({
      title: 'Delete Chatbot',
      message: 'Are you sure you want to delete the chatbot?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Delete the chatbot
        await api.delete('/chatbot', {
          params: {
            chatbot_id: selectedChatbotIdValue,
          },
        });
        // Notify the user about the successful deletion
        $q.notify({
          type: 'positive',
          message: 'Chatbot deleted successfully',
        });
        // Navigate back based on source route
        handleBackNavigation();
      })();
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to delete the chatbot: ' + String(error),
    });
  }
};

const fetchChatbotData = async (selectedChatbotIdValue: string) => {
  // Fetch chatbot information from the server
  const response = await api.get('/chatbot', {
    params: {
      chatbot_id: selectedChatbotIdValue,
    },
  });

  // Update the selectedChatbot with the fetched data
  form.value = {
    ...response.data,
    description:
      response.data.type_name === 'ChecklistChatBot'
        ? response.data.description
        : {
            checklist_items: '',
            session_summary_prompt: '',
          },
  };
};

onMounted(async () => {
  isFetching.value = true;

  selectedChatbotId.value = getStringParam(route.params.chatbotId || '');
  // Get source route and course ID from query parameters
  sourceRoute.value = typeof route.query.source === 'string' ? route.query.source : '';
  courseId.value = typeof route.query.courseId === 'string' ? route.query.courseId : '';

  // Fetch chatbot data
  await fetchChatbotData(selectedChatbotId.value);
  isFetching.value = false;
});
</script>

<style scoped></style>
