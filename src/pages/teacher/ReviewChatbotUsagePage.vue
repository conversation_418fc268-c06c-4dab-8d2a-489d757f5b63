<template>
  <q-page>
    <div class="q-banner text-h6 q-pa-md row q-gutter-sm">
      <q-btn flat dense round icon="arrow_back" :to="urlToCourse(courseId)">
        <q-tooltip>Back to Course</q-tooltip>
      </q-btn>
      <div>{{ courseTitle }} - {{ moduleTitle }} - {{ chatbotName }} - Usage Review</div>
      <q-btn flat dense round icon="refresh" @click="refreshData">
        <q-tooltip>Refresh Data</q-tooltip>
      </q-btn>
    </div>
    <div class="q-pa-md q-gutter-sm" v-if="!isFetching">
      <div class="text-h6">Student List ({{ studentGroupName }})</div>

      <q-input clearable v-model="filterStudents" label="Filter Students" debounce="300" />

      <q-checkbox
        v-model="showStudentsWithSessionsOnly"
        label="Only show students with Chatbot sessions"
      />

      <q-table
        :rows-per-page-options="[10, 20]"
        :columns="columns"
        :rows="filteredRows"
        :filter="filterStudents"
      >
        <template v-slot:header="props">
          <q-tr :props="props">
            <q-th auto-width />
            <q-th v-for="col in props.cols" :key="col.name" :props="props">
              {{ col.label }}
            </q-th>
          </q-tr>
        </template>
        <template v-slot:body="props">
          <q-tr :props="props">
            <q-td auto-width>
              <q-btn
                flat
                dense
                round
                icon="question_answer"
                @click="handleReviewStudentChatHistory(props.row)"
              />
            </q-td>
            <q-td v-for="col in props.cols" :key="col.name" :props="props">
              <template
                v-if="
                  col.name === 'checklist_progress' && props.row.checklist_progress === undefined
                "
              >
                N/A
              </template>
              <template v-else>
                {{ col.value }}
              </template>
              <div
                v-if="
                  col.name === 'checklist_progress' &&
                  props.row.checklist_progress !== undefined &&
                  chatbotType === 'ChecklistChatBot'
                "
              >
                <q-linear-progress :value="props.row.checklist_progress" color="positive" />
                <div class="text-caption text-center q-mt-sm">
                  {{ Math.round(props.row.checklist_progress * 100) }}% Complete
                </div>
              </div>
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
// import { useQuasar } from 'quasar';
import { api } from 'boot/axios';

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

// const $q = useQuasar();
const router = useRouter();
const route = useRoute();

const isFetching = ref(true);

const courseId = ref<string>('');
const chatbotId = ref<string>('');
const chatbotName = ref<string>('');
const chatbotType = ref<string>('');
const courseTitle = ref<string>('');
const moduleId = ref<string>('');
const moduleTitle = ref<string>('');
const urlToCourse = (courseId: string) => `/teacher/course/${courseId}`;
// const email = ref<string>('');
const studentGroupName = ref<string>('');
const studentGroupId = ref<string>('');
// const newStudentGroupId = ref<string>('');
const filterStudents = ref<string>('');
const showStudentsWithSessionsOnly = ref<boolean>(false);

// Cache keys
const CACHE_PREFIX = 'chatbot_usage_';
const CACHE_EXPIRY_TIME = 10 * 60 * 1000; // 10 minutes in milliseconds

const getCacheKey = (key: string): string => {
  return `${CACHE_PREFIX}${chatbotId.value}_${moduleId.value}_${key}`;
};

const saveToCache = <T,>(key: string, data: T): void => {
  const cacheData = {
    timestamp: Date.now(),
    data: data,
  };
  localStorage.setItem(getCacheKey(key), JSON.stringify(cacheData));
};

const getFromCache = <T,>(key: string): T | null => {
  const cacheData = localStorage.getItem(getCacheKey(key));
  if (!cacheData) return null;

  const { timestamp, data } = JSON.parse(cacheData);
  if (Date.now() - timestamp > CACHE_EXPIRY_TIME) {
    // Cache expired
    localStorage.removeItem(getCacheKey(key));
    return null;
  }

  return data;
};

const clearCache = (): void => {
  // Clear all cache for this chatbot and module
  Object.keys(localStorage)
    .filter((key) => key.startsWith(`${CACHE_PREFIX}${chatbotId.value}_${moduleId.value}`))
    .forEach((key) => localStorage.removeItem(key));
};

interface ChecklistProgressItem {
  item_id: string;
  item_content: string;
  completed: boolean;
}

interface QuantitativeReport {
  turn_count: number;
  user_word_count: number;
  chatbot_word_count: number;
  conversation_time: number;
}

interface QualitativeReport {
  session_summary: string;
}

interface SessionData {
  checklist_progress: ChecklistProgressItem[];
  quantitative_report: QuantitativeReport;
  qualitative_report: QualitativeReport;
}

interface StudentListItem {
  group_course_id: string;
  group_id: string;
  coruse_id: string;
  group_name: string;
  student_id: string;
  username: string;
  full_name: string;
  email: string;
  personnel_id: string;
}

interface Row {
  personnel_id: string;
  email: string;
  full_name: string;
  session_count?: number;
  turn_count?: number;
  user_word_count?: number;
  chatbot_word_count?: number;
  conversation_time?: number;
  checklist_progress?: number | undefined;
}

interface ChatbotDetail {
  chatbot_id: string;
  id: number;
  created_at: string;
  chatbot_name: string;
  model_name: string;
  system_prompt: string;
  welcome_prompt: string;
  temperature: number;
  type_name: string;
  description: {
    checklist_items: string;
    session_summary_prompt: string;
  };
  knowledge_base_persist_directory: string;
  knowledge_base_file_paths: string[];
  knowledge_base_file_names: string[];
  knowledge_base_embedding_model: string;
  updated_at: string;
  deleted_at: string;
}

interface Column {
  name: string;
  label: string;
  align: 'left' | 'right' | 'center';
  field: string | ((row: Row) => number | string);
  sortable: boolean;
  required?: boolean;
}

const columns = computed<Column[]>(() => {
  const baseColumns: Column[] = [
    {
      name: 'Index',
      required: true,
      label: 'Index',
      align: 'left',
      field: (row: Row) => rows.value.indexOf(row) + 1,
      sortable: true,
    },
    {
      name: 'personnel_id',
      label: 'Student ID',
      align: 'left',
      field: 'personnel_id',
      sortable: true,
    },
    {
      name: 'email',
      label: 'Email',
      align: 'left',
      field: 'email',
      sortable: true,
    },
    {
      name: 'full_name',
      label: 'Full Name',
      align: 'left',
      field: 'full_name',
      sortable: true,
    },
    {
      name: 'session_count',
      label: 'Session Count',
      align: 'center',
      field: 'session_count',
      sortable: true,
    },
    {
      name: 'turn_count',
      label: 'Turns',
      align: 'center',
      field: 'turn_count',
      sortable: true,
    },
    {
      name: 'user_word_count',
      label: 'User Words',
      align: 'center',
      field: 'user_word_count',
      sortable: true,
    },
    {
      name: 'chatbot_word_count',
      label: 'Chatbot Words',
      align: 'center',
      field: 'chatbot_word_count',
      sortable: true,
    },
    // {
    //   name: 'conversation_time',
    //   label: 'Conv. Time (min)',
    //   align: 'center',
    //   field: (row: Row) => (row.conversation_time ? Math.round(row.conversation_time / 60) : 0),
    //   sortable: true,
    // },
  ];

  // Only add checklist_progress column if chatbot type is ChecklistChatBot
  if (chatbotType.value === 'ChecklistChatBot') {
    baseColumns.push({
      name: 'checklist_progress',
      label: 'Checklist Progress',
      align: 'center',
      field: 'checklist_progress',
      sortable: true,
    });
  }

  return baseColumns;
});

const rows = ref<Row[]>([
  {
    personnel_id: '1',
    email: '<EMAIL>',
    full_name: 'John Doe',
  },
  {
    personnel_id: '2',
    email: '<EMAIL>',
    full_name: 'Jane Doe',
  },
]);

const filteredRows = ref<Row[]>([]);

const courseStudentList = ref<StudentListItem[]>([
  {
    group_course_id: '1',
    group_id: '1',
    coruse_id: '1',
    group_name: 'Group 1',
    student_id: '1',
    username: 'johndoe',
    full_name: 'John Doe',
    email: '<EMAIL>',
    personnel_id: '1',
  },
]);

const ChatbotUsageSessionListForAllUsers = ref([
  {
    user_id: '1',
    session_id: '1',
    session_name: 'Session 1 (Latest)',
    session_index: 1,
  },
]);

const isChatbotSessionLoaded = ref(false);
const isSessionDataLoaded = ref(false);
const sessionData = ref<{ [key: string]: SessionData }>({});

const handleReviewStudentChatHistory = async (row: Row) => {
  // Handle reviewing student chat history
  // Get the student_id from courseStudentList by row
  const studentId = courseStudentList.value.find(
    (student) => student.email === row.email,
  )?.student_id;
  // Redirect to the chat history page
  await router.push({
    path: `/teacher/chatbot/${chatbotId.value}/usage/${studentId}/session/latest`,
    query: {
      chatbotName: chatbotName.value,
      courseId: courseId.value,
      courseTitle: courseTitle.value,
      moduleId: moduleId.value,
      moduleTitle: moduleTitle.value,
    },
  });
};

const fetchCourseStudentList = async (courseId: string) => {
  // Check cache first
  const cachedData = getFromCache<Row[] | StudentListItem[]>('course_students');
  if (cachedData) {
    rows.value = cachedData as Row[];
    courseStudentList.value = cachedData as StudentListItem[];
    return;
  }

  // Fetch course student list from the server
  const response = await api.get('/course-student-list', {
    params: {
      course_id: courseId,
    },
  });
  // Update the rows with the fetched data
  rows.value = response.data;
  courseStudentList.value = response.data;

  // Save to cache
  saveToCache('course_students', response.data);
};

const fetchGroupInfo = async (courseId: string) => {
  // Check cache first
  interface GroupInfo {
    group_name: string;
    group_id: string;
  }
  const cachedData = getFromCache<GroupInfo>('group_info');
  if (cachedData) {
    studentGroupName.value = cachedData.group_name;
    studentGroupId.value = cachedData.group_id;
    return;
  }

  // Fetch group info from the server
  const response = await api.get('/group-by-course', {
    params: {
      course_id: courseId,
    },
  });
  // Update the student group information
  studentGroupName.value = response.data.group_name;
  studentGroupId.value = response.data.group_id;

  // Save to cache
  saveToCache('group_info', response.data);
};

const fetchChatbotUsageSessionListForAllUsers = async () => {
  // Check cache first
  type SessionListItem = {
    user_id: string;
    session_id: string;
    session_name: string;
    session_index: number;
  };
  const cachedData = getFromCache<SessionListItem[]>('usage_sessions');
  if (cachedData) {
    ChatbotUsageSessionListForAllUsers.value = cachedData;
    return;
  }

  // Fetch chatbot usage session list for all users
  const response = await api.get('/chatbot-usage-session-list-for-all-users', {
    params: {
      chatbot_id: chatbotId.value,
      module_id: moduleId.value,
    },
  });
  // Update the chatbot usage session list for all users
  ChatbotUsageSessionListForAllUsers.value = response.data;

  // Save to cache
  saveToCache('usage_sessions', response.data);
};

const fetchSessionDataForUser = async (userId: string, sessionId: string) => {
  // Check cache first
  const cachedData = getFromCache<SessionData>(`session_data_${userId}_${sessionId}`);
  if (cachedData) {
    return cachedData;
  }

  try {
    const response = await api.get('/chat-session-description', {
      params: {
        session_id: sessionId,
      },
    });

    const data = response.data.description || {
      checklist_progress: [],
      quantitative_report: {
        turn_count: 0,
        user_word_count: 0,
        chatbot_word_count: 0,
        conversation_time: 0,
      },
      qualitative_report: {
        session_summary: '',
      },
    };

    // Save to cache
    saveToCache(`session_data_${userId}_${sessionId}`, data);
    return data;
  } catch (error) {
    console.error(`Error loading session data for user ${userId}:`, error);
    return null;
  }
};

const fetchAllSessionData = async () => {
  // Create a list of promises for fetching session data
  const batchSize = 10; // Limit concurrent requests to avoid overwhelming the server
  const studentsWithSessions = [];

  // Prepare the list of students who have sessions
  for (const student of courseStudentList.value) {
    const studentSessions = ChatbotUsageSessionListForAllUsers.value.filter(
      (session) => session.user_id === student.student_id,
    );

    if (studentSessions.length > 0) {
      // Sort by session_index to get the latest
      studentSessions.sort((a, b) => b.session_index - a.session_index);
      const latestSession = studentSessions[0];

      if (latestSession && latestSession.session_id) {
        studentsWithSessions.push({
          studentId: student.student_id,
          sessionId: latestSession.session_id,
        });
      }
    }
  }

  // Process students in batches
  for (let i = 0; i < studentsWithSessions.length; i += batchSize) {
    const batch = studentsWithSessions.slice(i, i + batchSize);

    // Create a batch of promises
    const batchPromises = batch.map(({ studentId, sessionId }) =>
      fetchSessionDataForUser(studentId, sessionId).then((data) => {
        if (data) {
          sessionData.value[studentId] = data;
        }
        return { studentId, data };
      }),
    );

    // Wait for the current batch to complete before starting the next one
    await Promise.all(batchPromises);
  }

  isSessionDataLoaded.value = true;
  updateRowsWithSessionData();
};

const updateRowsWithSessionData = () => {
  // Update rows with session data
  for (const row of rows.value) {
    const student = courseStudentList.value.find((s) => s.email === row.email);
    if (!student) continue;

    const studentId = student.student_id;
    const studentSessions = ChatbotUsageSessionListForAllUsers.value.filter(
      (session) => session.user_id === studentId,
    );

    // Set session count
    row.session_count = studentSessions.length;

    // Set session data if available
    if (sessionData.value[studentId]) {
      const data = sessionData.value[studentId];

      // Set quantitative report data
      if (data.quantitative_report) {
        row.turn_count = data.quantitative_report.turn_count;
        row.user_word_count = data.quantitative_report.user_word_count;
        row.chatbot_word_count = data.quantitative_report.chatbot_word_count;
        row.conversation_time = data.quantitative_report.conversation_time;
      }

      // Calculate checklist progress only for ChecklistChatBot
      if (
        chatbotType.value === 'ChecklistChatBot' &&
        data.checklist_progress &&
        Array.isArray(data.checklist_progress) &&
        data.checklist_progress.length > 0
      ) {
        const completedItems = data.checklist_progress.filter((item) => item.completed).length;
        row.checklist_progress = completedItems / data.checklist_progress.length;
      } else {
        row.checklist_progress = undefined;
      }
    } else {
      row.turn_count = 0;
      row.user_word_count = 0;
      row.chatbot_word_count = 0;
      row.conversation_time = 0;
      row.checklist_progress = undefined;
    }
  }

  // Update filtered rows
  filteredRows.value = [...rows.value];
};

const filterStudentsWithSessions = () => {
  filteredRows.value = courseStudentList.value.filter((student) =>
    ChatbotUsageSessionListForAllUsers.value.some(
      (session) => session.user_id === student.student_id,
    ),
  );
};

const resetStudentList = () => {
  // Reset the filteredRows to the rows
  filteredRows.value = [...rows.value];
};

watch(showStudentsWithSessionsOnly, async (newValue) => {
  if (newValue) {
    if (!isChatbotSessionLoaded.value) {
      await fetchChatbotUsageSessionListForAllUsers();
      isChatbotSessionLoaded.value = true;

      // Fetch session data if not already loaded
      if (!isSessionDataLoaded.value) {
        await fetchAllSessionData();
      }
    }
    filterStudentsWithSessions();
  } else {
    resetStudentList();
  }
});

const fetchChatbotDetails = async (chatbotId: string) => {
  // Check cache first
  const cachedData = getFromCache<ChatbotDetail>('chatbot_details');
  if (cachedData) {
    chatbotType.value = cachedData.type_name;
    return;
  }

  try {
    const response = await api.get<ChatbotDetail>('/chatbot', {
      params: {
        chatbot_id: chatbotId,
      },
    });
    chatbotType.value = response.data.type_name;

    // Save to cache
    saveToCache('chatbot_details', response.data);
  } catch (error) {
    console.error('Error loading chatbot details:', error);
  }
};

const refreshData = async () => {
  clearCache();
  isFetching.value = true;

  // Refetch all data
  await fetchChatbotDetails(chatbotId.value);
  await fetchCourseStudentList(courseId.value);
  await fetchGroupInfo(courseId.value);
  await fetchChatbotUsageSessionListForAllUsers();
  isChatbotSessionLoaded.value = true;
  await fetchAllSessionData();
  filteredRows.value = [...rows.value];

  isFetching.value = false;
};

onMounted(async () => {
  isFetching.value = true;

  courseId.value = route.query.courseId as string;
  chatbotId.value = getStringParam(route.params.chatbotId || '');
  chatbotName.value = route.query.chatbotName as string;
  courseTitle.value = route.query.courseTitle as string;
  moduleId.value = route.query.moduleId as string;
  moduleTitle.value = route.query.moduleTitle as string;

  // Fetch chatbot details
  await fetchChatbotDetails(chatbotId.value);

  await fetchCourseStudentList(courseId.value);
  await fetchGroupInfo(courseId.value);

  // Fetch chatbot sessions
  await fetchChatbotUsageSessionListForAllUsers();
  isChatbotSessionLoaded.value = true;

  // Fetch session data
  await fetchAllSessionData();

  // Initialize the filteredRows with the rows
  filteredRows.value = [...rows.value];

  isFetching.value = false;
});
</script>

<style scoped>
.q-drawer {
  width: 300px;
}
</style>
