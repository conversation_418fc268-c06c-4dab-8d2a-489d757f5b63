<template>
  <q-layout view="hHh lpR fFf">
    <!-- Top Bar -->
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title class="text-weight-bold">
          <q-btn
            flat
            no-caps
            label="Bytewise"
            to="/start"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 0"
          />
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container>
      <q-page v-if="!isFetching">
        <div class="q-banner text-h6 q-pa-md row q-gutter-sm">
          <div>Activate Your Account</div>
        </div>
        <div class="q-pa-md column items-center justify-evenly">
          <div class="text-h6">{{ activationMessage }}</div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { api } from 'boot/axios';
import { useQuasar } from 'quasar';

const $q = useQuasar();

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};
const route = useRoute();
const router = useRouter();

const activationToken = ref<string>('');
const activationMessage = ref<string>('');

const isFetching = ref(true);

const activateAccount = async (token: string) => {
  try {
    const response = await api.put('/activate', {
      token: token,
    });
    // console.log(response.data);
    activationMessage.value = response.data.message;
    $q.notify({
      type: 'positive',
      message: response.data.message,
      position: 'top',
      timeout: 5000,
    });
    // Make sure DOM is updated UI before redirecting
    await nextTick();
    // Wait for 5 seconds before redirecting to login page
    await new Promise((resolve) => setTimeout(resolve, 5000));
    await router.push('/login'); // Await the router push
  } catch (error) {
    console.error(error);
    activationMessage.value = 'Failed to activate account. Please contact support.';
  }
};

onMounted(async () => {
  isFetching.value = true;
  activationToken.value = getStringParam(route.params.token || '');
  await activateAccount(activationToken.value);
  isFetching.value = false;
});
</script>

<style scoped></style>
