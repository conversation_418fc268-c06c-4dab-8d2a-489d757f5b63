<template>
  <q-page v-if="!isFetching" class="q-pa-md">
    <div class="text-h3 q-pa-md">My Account</div>

    <q-separator spaced />

    <div class="q-pa-md">
      <div class="text-h6 q-mb-sm">Personal Information</div>
      <q-list>
        <q-item>
          <q-item-section>
            <q-item-label caption>Username</q-item-label>
            <q-item-label>{{ user.username }}</q-item-label>
          </q-item-section>
        </q-item>

        <q-item>
          <q-item-section>
            <q-item-label caption>Full Name</q-item-label>
            <q-item-label>{{ user.full_name }}</q-item-label>
          </q-item-section>
        </q-item>

        <q-item>
          <q-item-section>
            <q-item-label caption>Email</q-item-label>
            <q-item-label>{{ user.email }}</q-item-label>
          </q-item-section>
        </q-item>

        <q-item>
          <q-item-section>
            <q-item-label caption>Role</q-item-label>
            <q-item-label>{{ user.role_name }}</q-item-label>
          </q-item-section>
        </q-item>

        <q-item>
          <q-item-section>
            <q-item-label caption>{{ user.role_name }} ID</q-item-label>
            <q-item-label>{{ user.personnel_id }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>

    <q-separator spaced v-if="user.role_name === 'Student'" />

    <div class="q-pa-md q-gutter-sm" v-if="user.role_name === 'Student'">
      <div class="text-h6 q-mb-sm">Joined Student Group</div>
      <div v-if="studentGroupList.length === 0">No student group joined</div>
      <div v-for="(studentGroup, index) in studentGroupList" :key="studentGroup.group_id">
        <div class="text-h6">Group {{ index + 1 }}</div>
        <q-list>
          <q-item>
            <q-item-section>
              <q-item-label caption>Group Name</q-item-label>
              <q-item-label>{{ studentGroup.group_name }}</q-item-label>
            </q-item-section>
          </q-item>
          <q-item v-if="studentGroup.description && studentGroup.description.content">
            <q-item-section>
              <q-item-label caption>Group Description</q-item-label>
              <q-item-label>{{ studentGroup.description.content }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>
    </div>

    <q-separator spaced />

    <!-- Change Password Section -->
    <div class="q-pa-md">
      <div class="text-h6 q-mb-md">Change Password</div>
      <q-input
        v-model="form.currentPassword"
        label="Current Password"
        type="password"
        clearable
        :rules="[(val) => !!val || 'Current Password is required']"
      />
      <q-input
        v-model="form.newPassword"
        label="Create a new password"
        clearable
        type="password"
        :rules="[
          (val) => !!val || 'Password is required',
          (val) => val.length >= 6 || 'Password must be at least 6 characters',
        ]"
      />
      <q-input
        v-model="form.newPasswordRepeat"
        label="Repeat the new password"
        clearable
        type="password"
        :rules="[(val) => val === form.newPassword || 'Passwords must match']"
      />
      <q-btn no-caps label="Save Changes" color="black" text-color="white" @click="onSaveChanges" />
    </div>

    <q-separator spaced />

    <!-- Subscription Status Section -->
    <!-- <div class="q-pa-md">
      <div class="text-h6 text-weight-bold q-mb-md">Subscription Status</div>
      <div>Plan Details: Gold Plan</div>
      <div>Renewal Date: October 15, 2023</div>
    </div> -->

    <!-- <q-separator spaced /> -->

    <!-- Activity Log Section -->
    <!-- <div class="q-pa-md">
      <div class="text-h6 text-weight-bold q-mb-md">Activity Log</div>
      <div v-for="log in activityLogs" :key="log.timestamp" class="q-mb-md">
        <div>Timestamp: {{ log.timestamp }}</div>
        <div>Activity: {{ log.activity }}</div>
      </div>
    </div> -->
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { api } from 'boot/axios';
import { onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useRouter } from 'vue-router';

const $q = useQuasar();
const router = useRouter();

interface User {
  user_id: string;
  username: string;
  full_name: string;
  email: string;
  role_name: string;
  personnel_id: string;
}

const user = ref<User>({
  user_id: '',
  username: '',
  full_name: '',
  email: '',
  role_name: '',
  personnel_id: '',
});

interface StudentGroup {
  enrollment_id: string;
  group_id: string;
  group_name: string;
  description: {
    content: string;
  };
}

const studentGroupList = ref<StudentGroup[]>([
  {
    enrollment_id: '',
    group_id: '',
    group_name: '',
    description: {
      content: '',
    },
  },
]);

const form = ref({
  currentPassword: '',
  newPassword: '',
  newPasswordRepeat: '',
});

const isFetching = ref<boolean>(true);

const fetchAccountInfo = async () => {
  // Fetch notification list from the server
  const response = await api.get('/user-info');
  // console.log(response.data);
  // Update the notificationList with the fetched data
  user.value = response.data;
};

const fetchJoinedStudentGroupList = async () => {
  // Fetch notification list from the server
  const response = await api.get('/joined-student-group-list');
  // console.log(response.data);
  // Update the notificationList with the fetched data
  studentGroupList.value = response.data;
};

const onSaveChanges = async () => {
  if (
    form.value.currentPassword === '' ||
    form.value.newPassword === '' ||
    form.value.newPasswordRepeat === ''
  ) {
    $q.notify({
      type: 'negative',
      message: 'Please fill in all fields',
    });
    return;
  }

  if (form.value.newPassword !== form.value.newPasswordRepeat) {
    $q.notify({
      type: 'negative',
      message: 'Passwords do not match',
    });
    return;
  }

  try {
    await api.put('/change-password', {
      current_password: form.value.currentPassword,
      new_password: form.value.newPassword,
    });
    // console.log(response.data);
    $q.notify({
      type: 'positive',
      message: 'Password changed successfully',
    });
    form.value.currentPassword = '';
    form.value.newPassword = '';
    form.value.newPasswordRepeat = '';

    // Remove the token from localStorage
    localStorage.removeItem('btws-tkn');
    // Redirect to the login page
    router
      .push('/start')
      .then(() => {
        $q.notify({
          type: 'positive',
          message: 'Logout success, please login again with the new password',
        });
      })
      .catch(() => {});
  } catch (error) {
    // console.error('Error changing password:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to change password: ' + String(error),
    });
  }
};

onMounted(async () => {
  isFetching.value = true;
  await fetchAccountInfo();
  if (user.value.role_name === 'Student') {
    await fetchJoinedStudentGroupList();
  }
  isFetching.value = false;
});
</script>

<style scoped>
.q-item {
  padding: 8px 0px;
}
</style>
